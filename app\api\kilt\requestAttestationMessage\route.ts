import * as Kilt from "@kiltprotocol/sdk-js"
import { Did, DidResourceUri, ICType } from "@kiltprotocol/sdk-js"
import kiltappUserCtype from "@/data/kilt/kiltappUserCtype.json"
import { encryptCallback, initializeKilt<PERSON><PERSON> } from "@/helpers/kilt"
import configuration from "@/configuration"

export const dynamic = "force-dynamic" // defaults to auto
export async function GET(request: Request) {
  const { dAppDidUri } = configuration
  const cType = kiltappUserCtype as ICType

  await initializeKiltApi()

  const { searchParams } = new URL(request.url)

  const encryptionKeyUri: DidResourceUri = searchParams.get(
    "encryptionKeyUri",
  ) as DidResourceUri
  const firstName = searchParams.get("firstName")
  const lastName = searchParams.get("lastName")
  const email = searchParams.get("email")

  if (!firstName || !lastName || !email || !encryptionKeyUri) {
    throw new Error("Missing parameters")
  }

  const { did: owner } = Did.parse(encryptionKeyUri)

  const claim = Kilt.Claim.fromCTypeAndClaimContents(
    cType,
    { name: `${firstName} ${lastName}`, email },
    owner,
  )

  const message = Kilt.Message.fromBody(
    {
      content: {
        claim,
        legitimations: [],
        cTypes: [cType],
      },
      type: "submit-terms",
    },
    dAppDidUri,
    owner,
  )

  const encryptedMessage = await Kilt.Message.encrypt(
    message,
    encryptCallback,
    encryptionKeyUri,
  )

  return Response.json({ encryptedMessage })
}
