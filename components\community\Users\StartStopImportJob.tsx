import { Button } from "@chakra-ui/react"
import React, { useState } from "react"
import { patchImportJob } from "@/services/api"
import useToast from "@/hooks/useToast"
import { ImportJob, ImportJobStatusChange } from "@/app/types/community"

type Props = {
  selectedImportJob: ImportJob | undefined
  onChangeImportStatus: () => Promise<void>
}

const StartStopImportJob = ({
  selectedImportJob,
  onChangeImportStatus,
}: Props) => {
  const { id } = selectedImportJob ?? {}
  const [isLoading, setIsLoading] = useState<ImportJobStatusChange | null>(null)

  const toast = useToast()
  const changeImportJobStatus = (status: ImportJobStatusChange) => async () => {
    if (!id) {
      return
    }

    try {
      setIsLoading(status)
      await patchImportJob(id, status)
      toast({
        description: `You have successfully ${status === "start" ? "started" : "stopped"} the import job.`,
      })
      await onChangeImportStatus()
    } catch (error: any) {
      toast({
        description: `Error ${status === "start" ? "starting" : "stopping"} import job.`,
        status: "error",
      })
    }
    setIsLoading(null)
  }

  const handleClickStart = changeImportJobStatus("start")
  const handleClickStop = changeImportJobStatus("stop")
  const stopEnabled =
    selectedImportJob?.status === "InProgress" ||
    selectedImportJob?.status === "Pending" ||
    selectedImportJob?.status === "Stopping"
  const startEnabled = selectedImportJob?.status === "Created"

  return (
    <>
      <Button
        variant="gray-outline"
        onClick={handleClickStart}
        isDisabled={!startEnabled || isLoading === "stop"}
        isLoading={isLoading === "start"}
      >
        Start
      </Button>
      <Button
        variant="gray-outline"
        onClick={handleClickStop}
        isDisabled={!stopEnabled || isLoading === "start"}
        isLoading={isLoading === "stop"}
      >
        Stop
      </Button>
    </>
  )
}

export default StartStopImportJob
