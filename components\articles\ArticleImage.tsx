import Image, { ImageProps } from "next/image"
import React from "react"
import { Box } from "@chakra-ui/react"

type Props = ImageProps

const ArticleImage = ({ alt, ...props }: Props) => {
  return (
    <Box my={2}>
      <Image
        sizes="100%"
        style={{
          width: "100%",
          height: "auto",
        }}
        alt={alt}
        {...props}
      />
    </Box>
  )
}

export default ArticleImage
