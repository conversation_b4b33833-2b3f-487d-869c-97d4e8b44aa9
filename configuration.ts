import { <PERSON><PERSON><PERSON> } from "@kiltprotocol/sdk-js"

const isServer = typeof window === "undefined"

if (!process.env.NEXT_PUBLIC_KILT_WSS_ADDRESS) {
  throw new Error("NEXT_PUBLIC_KILT_WSS_ADDRESS is not set")
}

if (isServer && !process.env.DAPP_DID_MNEMONIC) {
  throw new Error("DAPP_DID_MNEMONIC is not set")
}

if (!process.env.NEXT_PUBLIC_DAPP_DID_URI) {
  throw new Error("NEXT_PUBLIC_DAPP_DID_URI is not set")
}

if (isServer && !process.env.DAPP_ACCOUNT_MNEMONIC) {
  throw new Error("DAPP_ACCOUNT_MNEMONIC is not set")
}

if (isServer && !process.env.DAPP_ACCOUNT_ADDRESS) {
  throw new Error("DAPP_ACCOUNT_ADDRESS is not set")
}

if (!process.env.NEXT_PUBLIC_DAPP_NAME) {
  throw new Error("NEXT_PUBLIC_DAPP_NAME is not set")
}

if (isServer && !process.env.WELL_KNOWN_DID_CONFIGURATION) {
  throw new Error("WELL_KNOWN_DID_CONFIGURATION is not set")
}

if (!process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN) {
  throw new Error("NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN is not set")
}

if (isServer && !process.env.EXTERNAL_API_URL) {
  throw new Error("EXTERNAL_API_URL is not set")
}

if (isServer && !process.env.EXTERNAL_API_USERNAME) {
  throw new Error("EXTERNAL_API_USERNAME is not set")
}

if (isServer && !process.env.EXTERNAL_API_PASSWORD) {
  throw new Error("EXTERNAL_API_PASSWORD is not set")
}

if (!process.env.NEXT_PUBLIC_S3_IMAGES_URL) {
  throw new Error("NEXT_PUBLIC_S3_IMAGES_URL is not set")
}

const configuration = {
  kiltWssAddress: process.env.NEXT_PUBLIC_KILT_WSS_ADDRESS as string,
  dAppAccountMnemonic: process.env.DAPP_ACCOUNT_MNEMONIC as string,
  dAppAccountAddress: process.env.DAPP_ACCOUNT_ADDRESS as DidUri,
  dAppDidMnemonic: process.env.DAPP_DID_MNEMONIC as string,
  dAppDidUri: process.env.NEXT_PUBLIC_DAPP_DID_URI as DidUri,
  dAppName: process.env.NEXT_PUBLIC_DAPP_NAME as string,
  wellKnownDidConfiguration: process.env.WELL_KNOWN_DID_CONFIGURATION as string,
  mapboxAccessToken: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN as string,
  externalAPIURI: process.env.EXTERNAL_API_URL as string,
  externalAPIUsername: process.env.EXTERNAL_API_USERNAME as string,
  externalAPIPassword: process.env.EXTERNAL_API_PASSWORD as string,
  s3ImagesURL: process.env.NEXT_PUBLIC_S3_IMAGES_URL as string,
}

export default configuration
