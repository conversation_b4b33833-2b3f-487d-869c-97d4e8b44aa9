export const OWNERSHIP_STRUCTURE = [
  "Cooperative",
  "Publicly held corporation",
  "Privately held corporation",
  "Limited partnership",
  "Not-for-profit association",
  "Foundation",
]

export const BOARD_MEMBERS = [
  "Natural persons / citizens",
  "Local authorities",
  "Government agencies",
  "Schools and universities",
  "NGOs and associations",
  "SMEs",
  "Private energy companies or utilities",
  "Public energy companies or utilities",
  "Institutional investors",
  "Commercial banks",
  "State banks",
  "(Social) housing companies",
  "Other large companies",
  "Other energy communities",
]

export const VOTING_SYSTEM = [
  "One vote per member",
  "One vote per share",
  "No voting rights for members",
  "Shares without voting rights",
  "Founders have majority vote in the board",
]

export const STATUS = [
  "Informal group",
  "Legal structure with no activities / projects yet",
  "Legal structure with one ongoing activity / project",
  "Legal structure with multiple ongoing activities / projects",
  "Inactive / community has been dissolved",
]

export const HOW_DO_YOU_INVOLVE_MEMBERS_IN_DECISION_MAKING = [
  "General assembly",
  "Working committees / working groups",
  "Regular updates through e-mail",
]

export const ENERGY_COMMUNITY_TYPE = [
  "Renewable energy community",
  "Citizen energy community",
  "I don't know / Other",
]

export const COUNTRY = [
  "Austria",
  "Belgium",
  "Bulgaria",
  "Croatia",
  "Cyprus",
  "Czech Republic",
  "Denmark",
  "Estonia",
  "Finland",
  "France",
  "Germany",
  "Greece",
  "Hungary",
  "Ireland",
  "Italy",
  "Latvia",
  "Lithuania",
  "Luxembourg",
  "Malta",
  "Netherlands",
  "Poland",
  "Portugal",
  "Romania",
  "Slovakia",
  "Slovenia",
  "Spain",
  "Sweden",
]

export const LANGUAGE = [
  "Bulgarian",
  "Croatian",
  "Czech",
  "Danish",
  "Dutch",
  "English",
  "Estonian",
  "Finnish",
  "French",
  "German",
  "Greek",
  "Hungarian",
  "Irish",
  "Italian",
  "Latvian",
  "Lithuanian",
  "Maltese",
  "Polish",
  "Portuguese",
  "Romanian",
  "Slovak",
  "Slovenian",
  "Spanish",
  "Swedish",
]

export const ACTIVITIES = [
  "Self-consumption",
  "Electricity generation",
  "Energy efficiency",
  "Awareness campaigns",
]

export const SERVICES = [
  "Energy cafes / workshops",
  "Financial advice (i.e. on available subsidies & tax measures)",
  "Advice on switching energy supplier",
  "Education & training",
  "Energy monitoring",
]

export const CHALLENGES_IN_ESTABLISHING_THE_ENERGY_COMMUNITY = [
  "Regulatory (e.g. no supporting framework for energy communities)",
  "Administrative (e.g. complicated application procedures)",
]

export const CHALLENGES_IN_THE_LAST_YEAR = [
  "Regulatory (e.g. no supporting framework for energy communities)",
]

export const TSO = ["Tso 1", "Tso 2", "Tso 3"]

export const TYPES_OF_MEMBERSHIP = ["Type 1", "Type 2", "Type 3"]

export const CITIZENS_INDIVIDUAL = [
  "Citizens/Individual 1",
  "Citizens/Individual 2",
  "Citizens/Individual 3",
]

export const SMES = ["SMEs 1", "SMEs 2", "SMEs 3"]

export const MUNICIPALITIES = [
  "Municipalities 1",
  "Municipalities 2",
  "Municipalities 3",
]

export const ASSOCIATIONS = [
  "Associations 1",
  "Associations 2",
  "Associations 3",
]

export const OTHER = ["Other 1", "Other 2", "Other 3"]

export const SHARE_OF_FEMALE_MEMBERS = ["Share 1", "Share 2", "Share 3"]

export const SHARE_OF_VULNERABLE_MEMBERS = ["Share 1", "Share 2", "Share 3"]

export const SHARE_OF_MEMBERS_UNDER_35 = ["Share 1", "Share 2", "Share 3"]

export const PARTICIPATION_IN_LAST_YEARS_GENERAL_ASSEMBLY = [
  "Participation 1",
  "Participation 2",
  "Participation 3",
]
