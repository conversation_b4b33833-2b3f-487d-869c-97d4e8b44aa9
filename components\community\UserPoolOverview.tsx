import DataCard, { <PERSON>Field } from "@/components/DataCard"
import { formatDate } from "@/services/dates"
import React from "react"

type Props = {
  poolId: string
  arn: string
  name: string
  userCount: number
  createdAt: Date
  updatedAt: Date
}

const UserPoolOverview = ({
  poolId,
  arn,
  name,
  userCount,
  createdAt,
  updatedAt,
}: Props) => (
  <DataCard title="User pool overview">
    <DataField label="User pool name" value={name} />
    <DataField label="Estimated number of users" value={userCount} />
    <DataField label="Created time" value={formatDate(createdAt)} />
    <DataField label="User pool ID" value={poolId} copiable />
    <DataField label="ARN" value={arn} copiable />
    <DataField label="Last updated time" value={formatDate(updatedAt)} />
  </DataCard>
)

export default UserPoolOverview
