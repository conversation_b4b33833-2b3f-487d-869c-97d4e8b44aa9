"use client"

import React, { useState } from "react"
import { useLocale } from "next-intl"
import Content from "@/components/faq/Content"
import DescriptionModal from "@/components/faq/DescriptionModal"

const FAQ = () => {
  const [activeDescription, setActiveDescription] = useState<string | null>(
    null,
  )
  const locale = useLocale()

  const handleClickDescriptionLink = (key: string) => {
    setActiveDescription(key)
  }

  const handleCloseDescriptionModal = () => {
    setActiveDescription(null)
  }

  return (
    <>
      <Content
        locale={locale}
        onClickDescriptionLink={handleClickDescriptionLink}
      />
      <DescriptionModal
        activeDescription={activeDescription}
        onClickDescriptionLink={handleClickDescriptionLink}
        onClose={handleCloseDescriptionModal}
        locale={locale}
      />
    </>
  )
}

export default FAQ
