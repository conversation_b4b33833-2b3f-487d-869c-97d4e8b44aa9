"use client"

import { ChakraProvider } from "@chakra-ui/react"
import { ReactNode } from "react"
import SporranProvider from "./SporranProvider"
import theme from "@/theme/theme"

type Props = {
  children: ReactNode
}

const Providers = ({ children }: Props) => {
  return (
    <ChakraProvider theme={theme}>
      <SporranProvider>{children}</SporranProvider>
    </ChakraProvider>
  )
}

export default Providers
