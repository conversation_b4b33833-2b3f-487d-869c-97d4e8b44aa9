import { <PERSON>ack<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@chakra-ui/react"

const MissingSporranBody = () => {
  return (
    <Stack gap={4} sx={{ p: { color:"gray.500" } }}>
      <Text fontSize="lg">
        Decent.ec uses KILT for decentralized authentication.
      </Text>
      <Text fontSize="lg">
        To continue, please download and install the Sporran browser extension
        from the following link:{" "}
        <a
          href="https://www.sporran.org/"
          target="_blank"
          rel="noopener noreferrer"
        >
          Sporran.
        </a>
      </Text>
      <Text fontSize="lg">
        Once installed,{" "}
        <Button variant="link" onClick={() => window.location.reload()} color="blue.500">
          reload
        </Button>{" "}
        this page to proceed.
      </Text>
    </Stack>
  )
}

export default MissingSporranBody
