import { <PERSON>dal, Modal<PERSON>verlay, ModalContent, ModalHeader, ModalCloseButton, ModalBody } from '@chakra-ui/react'
import MissingSporranHeading from "@/components/missingSporran/MissingSporranHeading"
import MissingSporranBody from "@/components/missingSporran/MissingSporranBody"

type Props = {
  isOpen: boolean
  onClose: () => void
}

const MissingSporranModal = ({ isOpen, onClose }: Props) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      isCentered
    >
      <ModalOverlay />
      <ModalContent >
        <ModalHeader sx={{ h2: { fontSize: "xl" } }}>
          <MissingSporranHeading />
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody pb={6} sx={{ p: { fontSize: "medium" } }}>
          <MissingSporranBody />
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

export default MissingSporranModal
