import { createPoolUser, getPoolUsers } from "@/services/cognito/cognito"
import cognito from "@/data/cognito"

export async function GET(request: Request) {
  const filter = new URL(request.url).searchParams.get("filter")
  const users = await getPoolUsers(cognito.userPoolId, filter || "")

  return Response.json(users, { status: 200 })
}

export async function POST(request: Request) {
  const { username, email } = await request.json()

  try {
    const { User } = await createPoolUser(cognito.userPoolId, username, email)

    return Response.json(User, { status: 200 })
  } catch (error) {
    return Response.json({ error }, { status: 500 })
  }
}
