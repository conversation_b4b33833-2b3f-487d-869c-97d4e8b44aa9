"use client"

import {
  Box,
  Flex,
  Heading,
  IconButton,
  Radio,
  RadioGroup,
  SimpleGrid,
  Spinner,
  Stack,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  Card,
  CardBody,
  CardHeader,
} from "@chakra-ui/react"
import { MdRefresh } from "react-icons/md"
import Input from "@/components/form/Input"
import React, { useCallback, useEffect, useState } from "react"
import Pagination from "@/components/Pagination"
import { ImportJob } from "@/app/types/community"
import { getImportJobs } from "@/services/api"
import { formatDateDistance } from "@/services/dates"
import ImportStatus from "./ImportStatus"
import CreateImportJob from "./CreateImportJob"
import StartStopImportJob from "./StartStopImportJob"

const ImportUsers = () => {
  const [selectedImportJobId, setSelectedImportJobId] =
    useState<ImportJob["id"]>()
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [searchValue, setSearchValue] = useState<string>("")

  const [importJobs, setImportJobs] = useState<ImportJob[]>([])
  const filteredImportJobs = importJobs.filter((importJob) => {
    return importJob.name.toLowerCase().includes(searchValue.toLowerCase())
  })
  const selectedImportJob = importJobs.find(
    (importJob) => importJob.id === selectedImportJobId,
  )

  const handleChangeSelectedImportJob = (importJobId: ImportJob["id"]) => {
    setSelectedImportJobId(importJobId)
  }

  const [currentPage, setCurrentPage] = useState<number>(1)
  const resultsPerPage = 10
  const totalPages = Math.ceil(filteredImportJobs.length / resultsPerPage)
  const importJobsOfCurrentPage = filteredImportJobs.slice(
    (currentPage - 1) * resultsPerPage,
    currentPage * resultsPerPage,
  )

  const loadImportJobs = useCallback(async () => {
    setIsLoading(true)
    const importJobs = await getImportJobs()
    setImportJobs(importJobs)
    setIsLoading(false)
  }, [])

  useEffect(() => {
    void loadImportJobs()
  }, [loadImportJobs])

  if (importJobs.length === 0 && isLoading) {
    return <Spinner />
  }

  return (
    <Card>
      <CardHeader borderBottomWidth={1} mb={4}>
        <Flex align="center" justify="space-between">
          <Box w="60%">
            <Heading size="sm">Import users</Heading>
          </Box>
          <Flex gap={4}>
            <IconButton
              icon={<MdRefresh />}
              aria-label="Reload import jobs"
              variant="gray-outline"
              onClick={loadImportJobs}
              isLoading={isLoading}
            />
            <StartStopImportJob
              selectedImportJob={selectedImportJob}
              onChangeImportStatus={loadImportJobs}
            />
            <CreateImportJob onImportJobCreated={loadImportJobs} />
          </Flex>
        </Flex>
      </CardHeader>
      <CardBody pt={0}>
        <Stack spacing={6}>
          <SimpleGrid columns={2} gap={4}>
            <Input
              label="Search import jobs by job name"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
            />
          </SimpleGrid>
          <TableContainer borderWidth={1} borderRadius="lg">
            <RadioGroup
              onChange={handleChangeSelectedImportJob}
              value={selectedImportJobId}
            >
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th />
                    <Th>Job name</Th>
                    <Th>Status</Th>
                    <Th>Imported users</Th>
                    <Th>Skipped users</Th>
                    <Th>Failed users</Th>
                    <Th>Created time</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {importJobs.length === 0 ? (
                    <Tr>
                      <Td colSpan={7}>
                        <Flex
                          direction="column"
                          align="center"
                          justify="center"
                          h={32}
                          gap={2}
                        >
                          <Text fontSize="lg" color="gray.600">
                            No import jobs found.
                          </Text>
                          <CreateImportJob
                            onImportJobCreated={loadImportJobs}
                          />
                        </Flex>
                      </Td>
                    </Tr>
                  ) : (
                    importJobsOfCurrentPage.map(
                      ({
                        id,
                        name,
                        status,
                        importedUsers,
                        skippedUsers,
                        failedUsers,
                        createdAt,
                      }) => (
                        <Tr key={id}>
                          <Td maxW={2}>
                            <Radio value={id} />
                          </Td>
                          <Td>
                            <Text isTruncated maxW={24} title={name}>
                              {name}
                            </Text>
                          </Td>
                          <Td>
                            <ImportStatus status={status} />
                          </Td>
                          <Td>{importedUsers}</Td>
                          <Td>{skippedUsers}</Td>
                          <Td>{failedUsers}</Td>
                          <Td>{formatDateDistance(createdAt)}</Td>
                        </Tr>
                      ),
                    )
                  )}
                </Tbody>
              </Table>
            </RadioGroup>
          </TableContainer>
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            onChangePage={setCurrentPage}
          />
        </Stack>
      </CardBody>
    </Card>
  )
}

export default ImportUsers
