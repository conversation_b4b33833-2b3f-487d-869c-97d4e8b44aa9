import { Did<PERSON><PERSON> } from "@kiltprotocol/sdk-js"
import * as Kilt from "@kiltprotocol/sdk-js"
import { decryptCallback, initializeKiltA<PERSON> } from "@/helpers/kilt"
import { logIn } from "@/helpers/authentication"

export const dynamic = "force-dynamic" // defaults to auto

const isTrustedAttester = (attester: DidUri) => true //todo: implement

export async function POST(request: Request) {
  await initializeKiltApi()
  const { message } = await request.json()

  const decryptedMessage = await Kilt.Message.decrypt(message, decryptCallback)

  if (decryptedMessage.body.type !== "submit-credential") {
    throw new Error("Unexpected message type")
  }
  const credential = decryptedMessage.body.content[0]

  const { revoked, attester } =
    await Kilt.Credential.verifyPresentation(credential)

  if (revoked) {
    throw new Error("Credential has been revoked and hence it's not valid.")
  }
  if (isTrustedAttester(attester)) {
    logIn(credential.claim.contents.email.toString())
    return Response.json({ credentialVerified: true })
  }

  return Response.json({ credentialVerified: false }, { status: 401 })
}
