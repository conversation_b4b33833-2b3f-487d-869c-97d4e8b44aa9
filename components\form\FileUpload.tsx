import React, { ReactNode } from "react"
import { Accept, useDropzone } from "react-dropzone"
import {
  Box,
  Button,
  Flex,
  FormControl,
  FormLabel,
  IconButton,
  Text,
} from "@chakra-ui/react"
import { Control, FieldValues, Path, useController } from "react-hook-form"
import { MdOutlineCloudUpload } from "react-icons/md"
import { Image } from "@chakra-ui/next-js"

type Props<T extends FieldValues> = {
  label: string
  name: Path<T>
  control: Control<T>
  accept?: Accept
  description?: string | ReactNode
}
const Dropzone = <T extends FieldValues>({
  label,
  name,
  control,
  accept,
  description,
}: Props<T>) => {
  const {
    field: { value, onChange },
  } = useController({ name, control })

  const { getRootProps, getInputProps, open, acceptedFiles } = useDropzone({
    noClick: true,
    noKeyboard: true,
    onDrop: (acceptedFiles) => {
      onChange(acceptedFiles[0])
    },
    accept,
  })

  return (
    <FormControl>
      <FormLabel>{label}</FormLabel>
      <Flex
        direction="column"
        align="center"
        justify="center"
        w="full"
        h={140}
        gap={1}
        borderWidth={1}
        borderRadius="md"
        {...getRootProps({ className: "dropzone" })}
      >
        <input {...getInputProps()} />
        {acceptedFiles.length > 0 ? (
          <Box as="aside">
            <FilePreview file={acceptedFiles[0]} />
          </Box>
        ) : (
          <>
            <IconButton
              aria-label="Upload"
              icon={<MdOutlineCloudUpload />}
              onClick={open}
              variant="outline"
              colorScheme="gray"
              borderRadius="lg"
              mb={2}
            />
            <Text>
              <Button
                variant="link"
                onClick={open}
                color="decent-ec.500"
                fontWeight="bold"
                textDecoration="none"
              >
                Click to upload
              </Button>{" "}
              or drag and drop
            </Text>
            {description && (
              <Text fontSize="sm" color="gray.400">
                {description}
              </Text>
            )}
          </>
        )}
      </Flex>
    </FormControl>
  )
}

const FilePreview = ({ file }: { file: File }) => {
  if (file.type.split("/")[0] === "image") {
    return (
      <Image
        src={URL.createObjectURL(file)}
        width={100}
        height={100}
        objectFit="cover"
        alt="Uploaded file preview"
        borderRadius="md"
      />
    )
  }

  return <Text>{file.name}</Text>
}

export default Dropzone
