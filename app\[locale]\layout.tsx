import type { Metada<PERSON> } from "next"
import React, { ReactNode } from "react"
import Providers from "@/components/Providers"
import Layout from "@/components/Layout"
import { getMessages } from "next-intl/server"
import { NextIntlClientProvider } from "next-intl"

export const metadata: Metadata = {
  title: "Decent.ec",
  description:
    "Decent.ec is a community portal for decentralized energy communities",
}

type Props = {
  children: ReactNode
  params: Promise<{ locale: string }>
}

const RootLayout = async ({ children, params }: Props) => {
  const { locale } = await params

  const messages = await getMessages()

  return (
    <html lang={locale}>
      <body>
        <Providers>
          <NextIntlClientProvider messages={messages}>
            <Layout>{children}</Layout>
          </NextIntlClientProvider>
        </Providers>
      </body>
    </html>
  )
}

export default RootLayout
