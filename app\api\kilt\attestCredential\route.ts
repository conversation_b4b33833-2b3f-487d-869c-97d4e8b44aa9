import * as Kilt from "@kiltprotocol/sdk-js"
import {
  attestCredential,
  decryptCallback,
  initializeKiltApi,
} from "@/helpers/kilt"
import configuration from "@/configuration"
import { logIn } from "@/helpers/authentication"
export const dynamic = "force-dynamic" // defaults to auto

export async function POST(request: Request) {
  const { dAppDidUri, dAppDidMnemonic, dAppAccountMnemonic } = configuration

  await initializeKiltApi()
  const { message } = await request.json()

  const decryptedMessage = await Kilt.Message.decrypt(message, decryptCallback)
  if (decryptedMessage.body.type !== "request-attestation") {
    throw new Error("Unexpected message type")
  }

  const attesterAccount =
    Kilt.Utils.Crypto.makeKeypairFromUri(dAppAccountMnemonic)

  const assertionMethod = Kilt.Utils.Crypto.makeKeypairFromUri(dAppDidMnemonic)
  const attesterDidUri = dAppDidUri

  const { credential } = decryptedMessage.body.content

  await attestCredential(
    attesterAccount,
    attesterDidUri,
    credential,
    async ({ data }) => ({
      signature: assertionMethod.sign(data),
      keyType: assertionMethod.type,
    }),
  )

  logIn(credential.claim.contents.email.toString())
  return Response.json({ credential })
}
