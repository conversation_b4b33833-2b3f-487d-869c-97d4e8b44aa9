import { getImportJobs, postImportJob } from "@/services/cognito/cognito"
import { format } from "date-fns"
import { ImportJobStartAction } from "@/app/types/community"
import cognito from "@/data/cognito"

export async function GET(request: Request) {
  const users = await getImportJobs(cognito.userPoolId)

  return Response.json(users, { status: 200 })
}

export async function POST(request: Request) {
  const formData = await request.formData()
  const usersFile = formData.get("usersFile") as File
  const action = formData.get("action") as ImportJobStartAction
  const jobName =
    "decent-ec-import-" + format(new Date(), "yyyy-MM-dd-HH-mm-ss")
  await postImportJob(
    cognito.cloudWatchLogsRoleArn,
    jobName,
    cognito.userPoolId,
    usersFile,
    action,
  )

  return Response.json(
    { message: "Import job created successfully." },
    { status: 200 },
  )
}
