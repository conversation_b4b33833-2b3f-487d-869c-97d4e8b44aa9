import { retrieveExternalAPIAccessToken } from "@/helpers/authentication"
import configuration from "@/configuration"

export async function POST(request: Request) {
  const accessToken = await retrieveExternalAPIAccessToken()
  const { externalAPIURI } = configuration

  await fetch(`${externalAPIURI}/ec`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${accessToken}`,
    },
    body: JSON.stringify(await request.json()),
  })

  return new Response(null, { status: 204 })
}
