import { extractLanguageCodeFromPathname } from "@/helpers/language"
import { usePathname } from "next/navigation"

const useLanguage = () => {
  const pathname = usePathname()

  const generateUrlWithLanguageCode = (url: string) => {
    const languageCode = extractLanguageCodeFromPathname(pathname)

    return `/${languageCode}${url}`
  }

  return { generateUrlWithLanguageCode }
}

export default useLanguage
