import { Button, Flex, I<PERSON><PERSON>utton } from "@chakra-ui/react"
import React from "react"
import { MdDelete } from "react-icons/md"

const AssetActions = () => {
  const actions = [
    {
      label: "Edit asset",
      action: () => {},
    },
    {
      label: "Rename assets",
      action: () => {},
    },
    {
      label: "Localize assets",
      action: () => {},
    },
    {
      label: "Clone assets",
      action: () => {},
    },
    {
      label: "Edit consumption profile",
      action: () => {},
    },
    {
      label: "Edit production profile",
      action: () => {},
    },
    {
      label: "Disable asset",
      action: () => {},
    },
  ]

  return (
    <Flex
      m={-6}
      mt={-1}
      px={6}
      py={4}
      borderTopWidth={1}
      bg="white"
      pos="relative"
      shadow="top"
      flexWrap="wrap"
      gap={4}
    >
      {actions.map(({ label, action }) => (
        <Button
          key={label}
          variant="outline"
          colorScheme="gray"
          onClick={action}
        >
          {label}
        </Button>
      ))}
      <IconButton
        icon={<MdDelete size={20} />}
        variant="outline"
        colorScheme="red"
        ml="auto"
        aria-label="Delete"
      >
        KUKA
      </IconButton>
    </Flex>
  )
}

export default AssetActions
