import React, { ReactNode } from "react"
import { Button, Text } from "@chakra-ui/react"

const DESCRIPTIONS = ({
  onClickDescriptionLink,
  locale,
}: {
  onClickDescriptionLink: (key: string) => void
  locale: string
}): { [key: string]: ReactNode } => {
  if (locale === "hu") {
    return {
      signUpPage: (
        <Text>
          <Text as="strong">A Regisztrációs oldal</Text> egy egyszerű űrlap,
          ahol a potenciális energiaközösség-tagok regisztrálnak az e-mail
          címükkel vagy a telefonszámukkal, és megkapják a tagsághoz szükséges
          hitelesítő adatokat. Ezenkívül elfogadják a GDPR feltételeit, így
          hírleveleket küldhetsz nekik. A tagsági hitelesítő adatok hozzáférést
          biztosítanak az
          <Button
            variant="link"
            onClick={() => onClickDescriptionLink("energyCommunityPortal")}
          >
            energiaközösségi portálhoz
          </Button>
          , ahol feltölthetik a fogyasztási/termelési idősor adataikat, illetve
          szimulálhatják a megújuló energia termelését, tárolását, HVAC
          rendszerek működését és az e-mobilitást az AI eszközeink segítségével.
        </Text>
      ),
      energyCommunityPortal: (
        <Text>
          <Text as="strong">Az Energiaközösségi portál</Text> az a hely, ahol a
          közösségi tagok valós idejű adatokhoz férhetnek hozzá, válaszokat
          találnak a gyakran felmerülő kérdésekre, megvitathatják a közösséggel
          kapcsolatos (vagy attól független) témákat, szavazhatnak
          határozatokról, bekapcsolódhatnak különböző tagsági lehetőségekbe (pl.
          kereslet/válasz programokba), megtekinthetik az egyenlegüket, és részt
          vehetnek a virtuális találkozókon.
        </Text>
      ),
      virtualSCADA: (
        <Text>
          <Text as="strong">A Virtuális SCADA</Text> a Felügyeleti Irányítás és
          Adatgyűjtés rövidítése, amely segíti a teljesítmény irányítását és
          figyelését, és elengedhetetlen az energiaiparban a maximális
          hatékonyság eléréséhez.
        </Text>
      ),
    }
  }

  return {
    signUpPage: (
      <Text>
        <Text as="strong">Sign up page</Text> is a basic form where potential
        energy community members register with email or phone number and receive
        member credentials. They also accept gdpr t&c so you can target them
        with newsletter campaigns. The member credentials provides them access
        to the
        <Button
          variant="link"
          onClick={() => onClickDescriptionLink("energyCommunityPortal")}
        >
          energy community portal
        </Button>{" "}
        where they can upload consumption/production time series data and/or
        simulate renewable generation, storage, HVAC, and e-mobility with our AI
        tools.
      </Text>
    ),
    energyCommunityPortal: (
      <Text>
        <Text as="strong">Energy community portal</Text> is where community
        members turn to access real time data, find answers to common questions,
        discuss topics related (or not) to the community, cast their vote on
        resolutions, opt in/out to various membership options (e.g.
        Demand/Response programs) consult their wallet balance and participate
        in virtual “Town Hall” meetings.
      </Text>
    ),
    virtualSCADA: (
      <Text>
        <Text as="strong">Virtual SCADA</Text> stands for Supervisory Control
        And Data Acquisition, they help control and monitor the performance and
        are essential to maximize performance in the energy sector.
      </Text>
    ),
  }
}

export default DESCRIPTIONS
