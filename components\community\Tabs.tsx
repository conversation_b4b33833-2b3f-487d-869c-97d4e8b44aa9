import React, { ReactNode } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>b<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>ab<PERSON>ane<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON><PERSON><PERSON>ab<PERSON>,
} from "@chakra-ui/react"
import Users from "./Users/<USER>"
import ImportUsers from "./Users/<USER>"
import SignUpPreview from "@/components/community/SignUpExperience/SignUpPreview"
import SignUpOptions from "@/components/community/SignUpExperience/SignUpOptions"
import SignInPreview from "@/components/community/SignInExperience/SignInPreview"

type Props = {
  userCount: number
}

const Tabs = ({ userCount }: Props) => (
  <ChakraTabs isLazy>
    <TabList>
      <Tab gap={2}>
        Users <Badge colorScheme="green">{userCount}</Badge>
      </Tab>
      <Tab>Sign-up experience</Tab>
      <Tab>Sign-in experience</Tab>
      <Tab>Messages</Tab>
    </TabList>
    <Tab<PERSON>anels>
      <TabPanel>
        <Users />
        <ImportUsers />
      </TabPanel>
      <TabPanel>
        <SignUpPreview />
        <SignUpOptions />
      </TabPanel>
      <TabPanel>
        <SignInPreview />
      </TabPanel>
      <TabPanel></TabPanel>
    </TabPanels>
  </ChakraTabs>
)

type TabPanelProps = {
  children?: ReactNode
}

const TabPanel = ({ children }: TabPanelProps) => (
  <ChakraTabPanel>
    <Stack gap={6}>{children}</Stack>
  </ChakraTabPanel>
)

export default Tabs
