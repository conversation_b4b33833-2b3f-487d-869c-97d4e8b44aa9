import { Button, ButtonProps } from "@chakra-ui/react"
import React from "react"
import { Control, FieldValues, Path, useController } from "react-hook-form"
import IntrinsicAttributes = React.JSX.IntrinsicAttributes

type Props<T, C extends FieldValues> = {
  name: Path<C>
  control: Control<C>
} & ButtonProps

const SubmitButton = <
  T extends IntrinsicAttributes & { label: string; value: string },
  C extends FieldValues,
>({
  name,
  control,
  value,
  ...props
}: Props<T, C>) => {
  const {
    field: { onChange },
  } = useController({ name, control })

  return (
    <Button
      type="submit"
      onClick={() => {
        onChange(value)
      }}
      {...props}
    ></Button>
  )
}

export default SubmitButton
