import { Divider, Flex, Heading, Text } from "@chakra-ui/react"
import React, { ReactNode } from "react"

type Props = {
  title: string
  description?: string
  actions?: ReactNode
}

const PageHeader = ({ title, description, actions }: Props) => (
  <>
    <Flex justify="space-between">
      <Flex direction="column" gap={2}>
        <Heading as="h1" size="lg">
          {title}
        </Heading>
        {description && <Text color="gray.500">{description}</Text>}
      </Flex>
      {actions}
    </Flex>
    <Divider my={5} />
  </>
)

export default PageHeader
