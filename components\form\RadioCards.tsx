import RadioCard from "@/components/form/RadioCard"
import {
  FormControl,
  FormLabel,
  HStack,
  SimpleGrid,
  useRadioGroup,
} from "@chakra-ui/react"
import React, { FC } from "react"
import IntrinsicAttributes = React.JSX.IntrinsicAttributes
import { Control, FieldValues, Path, useController } from "react-hook-form"

type Props<T, C extends FieldValues> = {
  label: string
  options: T[]
  CardComponent: FC<T>
  name: Path<C>
  control: Control<C>
}

const RadioCards = <
  T extends IntrinsicAttributes & { label: string; value: string },
  C extends FieldValues,
>({
  label,
  options,
  CardComponent,
  name,
  control,
}: Props<T, C>) => {
  const {
    field: { value, onChange },
  } = useController({ name, control })

  const { getRootProps, getRadioProps } = useRadioGroup({
    name,
    value,
    onChange,
  })

  const group = getRootProps()

  return (
    <FormControl>
      <FormLabel>{label}</FormLabel>
      <SimpleGrid columns={3} spacing={2.5} {...group}>
        {options.map((option) => {
          const { value } = option
          const radio = getRadioProps({ value })
          return (
            <RadioCard key={value} {...radio}>
              <CardComponent {...option} />
            </RadioCard>
          )
        })}
      </SimpleGrid>
    </FormControl>
  )
}

export default RadioCards
