import { Box, RadioProps, useRadio } from "@chakra-ui/react"

type Props = RadioProps

const RadioCard = (props: Props) => {
  const { getInputProps, getRadioProps } = useRadio(props)

  const input = getInputProps()
  const checkbox = getRadioProps()

  return (
    <Box as="label">
      <input {...input} />
      <Box
        {...checkbox}
        cursor="pointer"
        borderWidth={1}
        borderRadius="lg"
        _hover={{
          borderColor: "decent-ec.300",
        }}
        _checked={{
          borderColor: "decent-ec.500",
        }}
      >
        {props.children}
      </Box>
    </Box>
  )
}

export default RadioCard
