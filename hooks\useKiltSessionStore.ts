import { create, Store<PERSON><PERSON>, UseBoundStore } from "zustand"
import { PubSubSession } from "@/app/types/kilt"

type SessionStore = {
  session: PubSubSession | null
  sessionIsLoading: boolean
  setSessionIsLoading: (sessionIsLoading: boolean) => void
  setSession: (session: PubSubSession) => void
}

const useKiltSessionStore: UseBoundStore<StoreApi<SessionStore>> = create(
  (set) => ({
    session: null,
    sessionIsLoading: true,
    setSessionIsLoading: (sessionIsLoading: boolean) => set(() => ({ sessionIsLoading })),
    setSession: (session: PubSubSession) => set(() => ({ session })),
  }),
)

export default useKiltSessionStore
