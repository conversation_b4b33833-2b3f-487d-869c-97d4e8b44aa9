{"name": "decent.ec", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.556.0", "@aws-sdk/credential-providers": "^3.556.0", "@chakra-ui/next-js": "^2.2.0", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.16", "@fontsource/lexend-deca": "^5.0.8", "@hookform/resolvers": "^3.3.4", "@kiltprotocol/sdk-js": "^0.36.0-rc.1", "@mapbox/search-js-react": "^1.0.0-beta.18", "@types/react-copy-to-clipboard": "^5.0.7", "date-fns": "^3.6.0", "framer-motion": "^11.0.5", "lodash": "^4.17.21", "mapbox-gl": "3.4.0", "next": "15.3.3", "next-intl": "^3.17.2", "react": "19.1.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.50.1", "react-icons": "^5.0.1", "react-map-gl": "^7.1.7", "zod": "^3.22.4", "zustand": "^4.5.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@typescript-eslint/eslint-plugin": "^7.0.1", "eslint": "^8", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "prettier": "^3.2.5", "typescript": "^5"}, "resolutions": {"@kiltprotocol/type-definitions": "1.11502.0-rc.1", "@types/react": "19.1.6", "@types/react-dom": "19.1.5"}}