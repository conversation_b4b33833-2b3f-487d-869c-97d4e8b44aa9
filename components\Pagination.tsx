import { Button, ButtonGroup, Flex } from "@chakra-ui/react"
import { Md<PERSON>rrowBack, MdArrowForward } from "react-icons/md"

type Props = {
  totalPages: number
  currentPage: number
  onChangePage: (page: number) => void
}
const Pagination = ({ totalPages, currentPage, onChangePage }: Props) => {
  if (totalPages <= 1) {
    return null
  }

  return (
    <Flex justify="right">
      <ButtonGroup size="sm" isAttached>
        <Button
          variant="square-outline"
          leftIcon={<MdArrowBack size={20} />}
          onClick={() => onChangePage(currentPage - 1)}
          isDisabled={currentPage === 1}
        >
          Previous
        </Button>
        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
          <PageButton
            key={page}
            isCurrent={page === currentPage}
            onClick={() => onChangePage(page)}
          >
            {page}
          </PageButton>
        ))}
        <Button
          variant="square-outline"
          rightIcon={<MdArrowForward size={20} />}
          onClick={() => onChangePage(currentPage + 1)}
          isDisabled={currentPage === totalPages}
        >
          Next
        </Button>
      </ButtonGroup>
    </Flex>
  )
}

type PageButtonProps = {
  children: number
  onClick: () => void
  isCurrent?: boolean
}

const PageButton = ({ children, isCurrent, onClick }: PageButtonProps) => (
  <Button
    variant="square-outline"
    {...(isCurrent ? { bg: "gray.100" } : {})}
    onClick={onClick}
  >
    {children}
  </Button>
)

export default Pagination
