import { <PERSON>dal, ModalOverlay, Modal<PERSON>ontent, Modal<PERSON>eader, ModalBody, Text, Spinner, Stack } from '@chakra-ui/react'

const SignUpModal = ({isOpen, isLoading}: {isOpen: boolean, isLoading: boolean}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {}}
      isCentered
    >
      <ModalOverlay />
      <ModalContent>
        <ModalHeader textAlign="center">
          Creating your KILT credential.
        </ModalHeader>
        <ModalBody pb={4} textAlign="center">
          {isLoading ? (
            <Stack gap={4}>
              <Spinner size="xl" my={4} />
              <Text
                fontSize="lg"
                fontWeight="semibold"
                color="decent-ec.600"
              >
                Please wait while your credentials are being verified.
              </Text>
              <Text
                fontSize="lg"
                fontWeight="semibold"
                color="decent-ec.600"
              >
                This may take up to 60 seconds.
              </Text>
            </Stack>
            ) : (
            <Text
              fontSize="lg"
              fontWeight="semibold"
              color="decent-ec.600"
            >
              Please follow the instructions in your Sporran wallet and
              wait while we are issuing your KILT credential.
            </Text>
          )}
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

export default SignUpModal