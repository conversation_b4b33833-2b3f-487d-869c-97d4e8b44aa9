import {
  Button,
  Input,
  <PERSON><PERSON>,
  <PERSON>overBody,
  <PERSON>over<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ack,
  Text,
} from "@chakra-ui/react"
import React, { useRef } from "react"
import energyAssetTypes from "@/data/energyAssetTypes"
import { ASSET_ICON_SIZE } from "@/constants"
import { Image } from "@chakra-ui/next-js"

type Props = {
  onSelect: (assetType: string) => void
}

const AddEnergyAssetPopover = ({ onSelect }: Props) => {
  const initialFocusRef = useRef<HTMLInputElement | null>(null)
  const [searchString, setSearchString] = React.useState("")

  return (
    <Popover initialFocusRef={initialFocusRef} placement="bottom">
      <PopoverTrigger>
        <Button>Add</Button>
      </PopoverTrigger>
      <PopoverContent>
        <PopoverBody p={6}>
          <Input
            ref={initialFocusRef}
            placeholder="Search..."
            value={searchString}
            onChange={(e) => setSearchString(e.target.value)}
            borderRadius="xl"
          />
          <Stack spacing={2} align="flex-start" pt={2}>
            {energyAssetTypes
              .filter(({ name }) =>
                name.toLowerCase().includes(searchString.toLowerCase()),
              )
              .map(({ id, name, icon }) => (
                <Button
                  key={id}
                  onClick={() => onSelect(id)}
                  leftIcon={
                    <Image
                      src={`/assets/assetIcons/${icon}.svg`}
                      alt={name}
                      width={ASSET_ICON_SIZE}
                      height={ASSET_ICON_SIZE}
                      maxWidth={ASSET_ICON_SIZE}
                    />
                  }
                  variant="ghost"
                  colorScheme="gray"
                  w="full"
                  justifyContent="flex-start"
                  borderRadius="xl"
                  py={6}
                  fontWeight={500}
                >
                  <Text as="span" textOverflow="ellipsis" overflow="hidden">
                    {name}
                  </Text>
                </Button>
              ))}
          </Stack>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  )
}

export default AddEnergyAssetPopover
