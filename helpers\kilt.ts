import configuration from "@/configuration"
import * as Kilt from "@kiltprotocol/sdk-js"
import { fakeChallenge } from "@/components/kilt/Login"
import {
  Did,
  DidResourceUri,
  EncryptCallback,
  Utils,
} from "@kiltprotocol/sdk-js"
import { PubSubSession } from "@/app/types/kilt"
import {
  getIsLoggedIn,
  getRequestAttestationMessage,
  getRequestCredentialMessage,
  postAttestCredential,
  postLogOut,
  postVerifyCredential,
} from "@/services/api"

export const initializeKiltApi = async () => {
  const { kiltWssAddress } = configuration

  await Kilt.connect(kiltWssAddress)

  return Kilt.ConfigService.get("api")
}

export const createSporranSession = async () => {
  const { dAppDidUri, dAppName } = configuration

  const api = await initializeKiltApi()

  const encodedFullDid = await api.call.did.query(Kilt.Did.toChain(dAppDidUri))
  const { document } = Kilt.Did.linkedInfoFromChain(encodedFullDid)

  if (!document.keyAgreement || !document.keyAgreement[0]) {
    return
  }
  const dAppEncryptionKeyUri =
    `${document.uri}${document.keyAgreement[0].id}` as Kilt.DidResourceUri

  // todo: add kilt extension types
  // @ts-ignore
  return window.kilt.sporran.startSession(
    dAppName,
    dAppEncryptionKeyUri,
    fakeChallenge,
  )
}

// todo: add kilt extension types
// @ts-ignore
export const requestKiltCredentialFromSporran = async (session) => {
  const { encryptedMessage } = await getRequestCredentialMessage({
    encryptionKeyUri: session.encryptionKeyUri,
  })
  await session.send(encryptedMessage)

  await session.listen(async (message: Kilt.IEncryptedMessage) => {
    return await postVerifyCredential({ message })
  })
}

export const requestKiltAttestationFromSporran = async (
  firstName: string,
  lastName: string,
  email: string,
  session: PubSubSession,
  onAttestationRequestStart?: () => void,
) => {
  const { encryptedMessage } = await getRequestAttestationMessage({
    firstName,
    lastName,
    email,
    encryptionKeyUri: session.encryptionKeyUri,
  })
  await session.send(encryptedMessage)
  await session.listen(async (message) => {
    if (onAttestationRequestStart) onAttestationRequestStart()
    await postAttestCredential(message)
  })
}

export const generateDAppKeyAgreement = () =>
  Utils.Crypto.makeEncryptionKeypairFromSeed(
    Utils.Crypto.mnemonicToMiniSecret(configuration.dAppDidMnemonic),
  )

export const encryptCallback: Kilt.EncryptCallback = async ({
  data,
  peerPublicKey,
}: Parameters<EncryptCallback>[0]) => {
  const fullDid = await Did.resolve(configuration.dAppDidUri)
  const keyAgreementKeyId = fullDid?.document?.keyAgreement?.[0]?.id
  const fullDidDocumentUri = fullDid?.document?.uri

  if (!keyAgreementKeyId || !fullDidDocumentUri) {
    throw new Error("No key agreement found")
  }

  const { box, nonce } = Utils.Crypto.encryptAsymmetric(
    data,
    peerPublicKey,
    generateDAppKeyAgreement().secretKey,
  )

  return {
    data: box,
    nonce,
    keyUri: `${fullDidDocumentUri}${keyAgreementKeyId}` as DidResourceUri,
  }
}

export const decryptCallback: Kilt.DecryptCallback = async ({
  data,
  nonce,
  peerPublicKey,
}) => {
  const result = Kilt.Utils.Crypto.decryptAsymmetric(
    { box: data, nonce },
    peerPublicKey,
    generateDAppKeyAgreement().secretKey,
  )
  if (!result) {
    throw new Error("Cannot decrypt")
  }
  return {
    data: result,
  }
}

export async function attestCredential(
  attesterAccount: Kilt.KiltKeyringPair,
  attesterDid: Kilt.DidUri,
  credential: Kilt.ICredential,
  signCallback: Kilt.SignExtrinsicCallback,
): Promise<Kilt.ICredential> {
  const api = Kilt.ConfigService.get("api")

  // Get CType and root hash from the provided credential.
  const { cTypeHash, claimHash } = Kilt.Attestation.fromCredentialAndDid(
    credential,
    attesterDid,
  )

  // Create the tx and authorize it.
  const tx = api.tx.attestation.add(claimHash, cTypeHash, null)
  const extrinsic = await Kilt.Did.authorizeTx(
    attesterDid,
    tx,
    signCallback,
    attesterAccount.address,
  )

  // Submit the tx to write the attestation to the chain.
  await Kilt.Blockchain.signAndSubmitTx(extrinsic, attesterAccount)

  return credential
}

export async function logOut() {
  return postLogOut()
}

export async function isLoggedIn() {
  return getIsLoggedIn()
}
