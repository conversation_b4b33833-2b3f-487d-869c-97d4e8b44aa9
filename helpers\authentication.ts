import { cookies } from "next/headers"
import configuration from "@/configuration"

export const logIn = async (email: string) => {
  const cookieStore = await cookies()
  cookieStore.set("loggedIn", "true").set("loginEmail", email)
}

export const logOut = async () => {
  const cookieStore = await cookies()
  cookieStore.set("loggedIn", "false")
}

export const isLoggedIn = async () => {
  const cookieStore = await cookies()
  cookieStore.get("loggedIn")?.value === "true"
}

export const retrieveExternalAPIAccessToken = async () => {
  const { externalAPIURI, externalAPIUsername, externalAPIPassword } =
    configuration

  const response = await fetch(`${externalAPIURI}/auth/login`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name: externalAPIUsername,
      password: externalAPIPassword,
    }),
  })

  return ((await response.json()) as { token: string }).token
}
