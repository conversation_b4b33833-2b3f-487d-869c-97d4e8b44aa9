"use client"

import useKiltSessionStore from "@/hooks/useKiltSessionStore"
import React, { ReactNode, useEffect } from "react"
import { createSporranSession } from "@/helpers/kilt"

type Props = {
  children: ReactNode
}

const SporranProvider = ({ children }: Props) => {
  const {setSession, setSessionIsLoading } = useKiltSessionStore(
    (state) => ({
      setSession: state.setSession,
      setSessionIsLoading: state.setSessionIsLoading,
    }),
  )

  useEffect(() => {
    void createSporranSession().then((session) => {
      setSession(session)
    }).catch((error) => {
      console.error("Error creating Sporran session:", error)
    })
    .finally(() => {
      setSessionIsLoading(false)
    })
  }, [setSession, setSessionIsLoading])

  return (
    <>
      <script src="/scripts/kilt.js" async />
      {children}
    </>
  )
}

export default SporranProvider
