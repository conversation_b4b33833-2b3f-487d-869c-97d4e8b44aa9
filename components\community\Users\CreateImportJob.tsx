import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON><PERSON>on,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  ModalOverlay,
  Stack,
} from "@chakra-ui/react"
import React, { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import useToast from "@/hooks/useToast"
import { postImportJob } from "@/services/api"
import { ImportJob, ImportJobStartAction } from "@/app/types/community"
import { extractImportJob } from "@/services/cognito/cognito"
import FileUpload from "@/components/form/FileUpload"
import SubmitButton from "@/components/form/SubmitButton"

const schema = z.object({
  usersFile: z.instanceof(File),
  action: z.enum(["create", "createAndStart"]),
})

type FormValues = {
  usersFile: File
  action: ImportJobStartAction
}

type Props = {
  onImportJobCreated: (job: ImportJob) => void
}

const CreateImportJob = ({ onImportJobCreated }: Props) => {
  const toast = useToast()
  const [showModal, setShowModal] = useState<boolean>(false)
  const {
    handleSubmit,
    formState: { isValid, isSubmitting },
    control,
    reset,
    watch,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      action: "createAndStart",
    },
  })

  const onSubmit = async ({ usersFile, action }: FormValues) => {
    try {
      const importJob = await postImportJob(usersFile, action)

      toast({
        description: "You have successfully created an import job.",
      })
      onImportJobCreated(extractImportJob(importJob))
      setShowModal(false)
      reset()
    } catch (error: any) {
      toast({
        description: `Error creating import job: ${error?.cause?.error?.name}`,
        status: "error",
      })
    }
  }

  const handleClickAdd = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  return (
    <>
      <Button
        aria-label="Create Import Job"
        variant="gray"
        onClick={handleClickAdd}
      >
        Create import job
      </Button>
      <Modal isOpen={showModal} onClose={handleCloseModal} isCentered>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Create Import Job</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Stack spacing={4}>
                <FileUpload
                  label="CSV file"
                  control={control}
                  name="usersFile"
                  description="Select a CSV file to import users."
                  accept={{ "text/csv": [".csv"] }}
                />
              </Stack>
            </ModalBody>
            <ModalFooter gap={2}>
              <Button
                onClick={handleCloseModal}
                isDisabled={isSubmitting}
                variant="ghost"
                mr="auto"
              >
                Cancel
              </Button>
              <SubmitButton
                control={control}
                isLoading={isSubmitting && watch("action") === "create"}
                isDisabled={!isValid || isSubmitting}
                variant="outline"
                name="action"
                value="create"
              >
                Create
              </SubmitButton>
              <SubmitButton
                isLoading={isSubmitting && watch("action") === "createAndStart"}
                isDisabled={!isValid || isSubmitting}
                control={control}
                name="action"
                value="createAndStart"
              >
                Create and start
              </SubmitButton>
            </ModalFooter>
          </ModalContent>
        </form>
      </Modal>
    </>
  )
}

export default CreateImportJob
