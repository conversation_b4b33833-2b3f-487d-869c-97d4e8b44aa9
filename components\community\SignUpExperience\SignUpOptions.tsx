import { Heading, Text } from "@chakra-ui/react"

const SignUpOptions = () => (
  <>
    <Heading size="md">
      Attribute verification and user account confirmation
    </Heading>
    <Text>
      Choose between Cognito assisted and self-managed user attribute
      verification and account confirmation. Only verified attributes can be
      used for sign-in, account recovery, and MFA. A user account must be
      confirmed either by attribute verification, or user pool administrator
      confirmation, before a user is allowed to sign in.
    </Text>
  </>
)
export default SignUpOptions
