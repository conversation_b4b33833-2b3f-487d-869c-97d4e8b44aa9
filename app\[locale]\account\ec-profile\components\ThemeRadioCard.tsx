import { Box, Flex, HStack, Text } from "@chakra-ui/react"

type ThemeCardProps = {
  label: string
  value: string
  colors: string[]
}

const ThemeRadioCard = ({ label, colors }: ThemeCardProps) => {
  return (
    <Flex direction="column" p={4} gap={4}>
      <HStack spacing={0} borderRadius="md" pr={2.5}>
        {colors.map((color) => (
          <Box
            key={color}
            bg={color}
            mr={-2.5}
            w={9}
            h={9}
            borderRadius="md"
            boxShadow="inset 0 0 2px lightgrey"
          ></Box>
        ))}
      </HStack>
      <Text color="gray.500">{label}</Text>
    </Flex>
  )
}

export default ThemeRadioCard
