import { notFound } from "next/navigation"
import { getRequestConfig } from "next-intl/server"
import {routing} from ""

export const localesWithCountryNames = [
  { country: "English", code: "en" },
  { country: "Magyar", code: "hu" },
]

export default getRequestConfig(async ({ requestLocale }) => {
  let locale = await requestLocale
  if (!locale || !routing.locales.includes(locale as any)) {
    locale = defineRouting.defaultLocale;
  }

  const isValidLocale = localesWithCountryNames.some(
    (loc) => loc.code === locale,
  )

  if (!isValidLocale) {
    notFound()
  }

  return {
    messages: (await import(`@/messages/${locale}.json`)).default,
  }
})
