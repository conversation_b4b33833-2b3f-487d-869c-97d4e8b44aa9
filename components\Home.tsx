"use client"

import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Text } from "@chakra-ui/react"
import React from "react"
import PrimarySection from "@/components/PrimarySection"
import { Link } from "@chakra-ui/next-js"

const Home = () => {
  return (
    <PrimarySection>
      <Box w="full" maxW={750}>
        <Heading as="h1" size="2xl" mb={4}>
          Decent.ec offers a complete toolstack for decentralized energy
          communities
        </Heading>
        <Text fontSize="2xl" fontWeight={500} color="#6F7278" mb={6}>
          Register an account to launch a sustainable
          <br />
          energy community!
        </Text>
        {/*todo: style button using colorScheme*/}
        <Button as={Link} href="/registration" size="xl">
          Join Now
        </Button>
      </Box>
    </PrimarySection>
  )
}

export default Home
