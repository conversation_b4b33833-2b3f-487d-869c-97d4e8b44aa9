"use client"

import { Link } from "@chakra-ui/next-js"
import { Stack } from "@chakra-ui/react"
import {
  MdAddToQueue,
  MdGroup,
  MdOutlinePayment,
  MdOutlineTune,
  MdSunny,
  MdSwitchAccount,
} from "react-icons/md"
import { ReactNode } from "react"
import { usePathname } from "next/navigation"
import useLanguage from "@/hooks/useLanguage"

const AccountMenu = () => {
  const pathname = usePathname()
  const { generateUrlWithLanguageCode } = useLanguage()
  const menuItems = [
    {
      icon: <MdSwitchAccount />,
      href: generateUrlWithLanguageCode("/account/ec-profile"),
      label: "EC profile",
    },
    {
      icon: <MdSunny />,
      href: generateUrlWithLanguageCode("/account/energy-assets"),
      label: "Energy assets",
    },
    {
      icon: <MdGroup />,
      href: generateUrlWithLanguageCode("/account/community"),
      label: "Community",
    },
    {
      icon: <MdAddToQueue />,
      href: generateUrlWithLanguageCode("/account/modelling"),
      label: "Modelling",
    },
    {
      icon: <MdOutlinePayment />,
      href: generateUrlWithLanguageCode("/account/billing"),
      label: "Billing",
    },
    {
      icon: <MdOutlineTune />,
      href: generateUrlWithLanguageCode("/account/toolkit"),
      label: "Toolkit",
    },
  ]

  return (
    <Stack direction="column" p={6} spacing={3.5}>
      {menuItems.map(({ icon, href, label }) => {
        return (
          <MenuItem
            key={href}
            icon={icon}
            href={href}
            active={pathname === href}
          >
            {label}
          </MenuItem>
        )
      })}
    </Stack>
  )
}

type MenuItemProps = {
  icon: ReactNode
  href: string
  children: ReactNode
  active: boolean
}

const MenuItem = ({ icon, href, children, active }: MenuItemProps) => (
  <Link
    href={href}
    fontSize="xl"
    fontWeight="bold"
    display="flex"
    alignItems="center"
    gap={4}
    color={active ? "decent-ec.600" : "gray.900"}
    _hover={{ color: "decent-ec.600" }}
  >
    {icon} {children}
  </Link>
)

export default AccountMenu
