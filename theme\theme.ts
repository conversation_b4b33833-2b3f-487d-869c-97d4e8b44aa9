import { extendTheme, withDefaultColorScheme } from "@chakra-ui/react"
import "@fontsource/inter"
import "@fontsource/lexend-deca"
import { listTheme } from "./List"

const theme = extendTheme(
  {
    colors: {
      "decent-ec": {
        50: "#F4F9F5",
        100: "#E9F3EB",
        200: "#C7E0CB",
        300: "#A4CDA9",
        400: "#5FB870",
        500: "#20B24D",
        600: "#1E9646",
        700: "#0F6120",
        800: "#0B4818",
        900: "#072E0F",
      },
      green: {
        50: "#F4F9F5",
        100: "#E9F3EB",
        200: "#C7E0CB",
        300: "#A4CDA9",
        400: "#5FB870",
        500: "#20B24D",
        600: "#1E9646",
        700: "#0F6120",
        800: "#0B4818",
        900: "#072E0F",
      },
      gray: {
        50: "#F9FAFB",
        100: "#F3F4F6",
        200: "#E5E7EB",
        300: "#D1D5DB",
        400: "#A1A5AB",
        500: "#6B7280",
        600: "#4B5563",
        700: "#374151",
        800: "#1F2937",
        900: "#1C1D1F",
      },
    },
    fonts: {
      heading: "Inter",
      body: "Inter",
    },
    shadows: {
      top: "0px 5px 30px 0px lightgray",
    },
    components: {
      Text: {
        baseStyle: {
          color: "gray.900",
        },
      },
      Heading: {
        baseStyle: {
          color: "gray.900",
          fontWeight: "semibold",
        },
        defaultProps: {
          size: "xl",
        },
        sizes: {
          xl: {
            fontSize: "2.65rem",
          },
        },
      },
      Button: {
        baseStyle: {
          fontWeight: "bold",
          borderRadius: "full",
          _hover: {
            textDecoration: "none",
          },
        },
        variants: {
          link: {
            fontWeight: 500,
            textDecoration: "underline",
            color: "inherit",
          },
          buttonLink: {
            fontWeight: 500,
            textDecoration: "none",
          },
          "square-outline": {
            borderWidth: 1,
            borderColor: "gray.300",
            borderRadius: "lg",
            fontWeight: 600,
            color: "gray.700",
            minW: "2.5rem",
            minH: "2.5rem",
            bg: "white",
            mx: "-1px",
            _hover: {
              bg: "gray.100",
            },
          },
          "gray-outline": {
            color: "gray.500",
            borderWidth: 1,
            size: "md",
            _hover: {
              bg: "gray.100",
            },
          },
          gray: {
            bg: "gray.700",
            color: "white",
            borderWidth: 1,
            size: "md",
            _hover: {
              bg: "gray.600",
            },
          },
        },
        sizes: {
          lg: {
            fontSize: "xl",
            h: 12,
            px: 6,
          },
          xl: {
            h: 14,
            fontSize: "xl",
            px: 8,
          },
        },
      },
      Checkbox: {
        baseStyle: {
          label: {
            color: "gray.500",
          },
          control: {
            borderRadius: "md",
            borderWidth: 1,
            bg: "white",
          },
        },
        sizes: {
          md: {
            control: {
              width: 5,
              height: 5,
            },
          },
          "2xs": {
            control: {
              width: 5,
              height: 5,
            },
            label: {
              size: "2xs",
            },
          },
        },
      },
      Radio: {
        baseStyle: {
          label: {
            color: "gray.500",
          },
          control: {
            borderWidth: 1,
            bg: "white",
          },
        },
        sizes: {
          md: {
            control: {
              width: 5,
              height: 5,
            },
          },
          "2xs": {
            control: {
              width: 5,
              height: 5,
            },
            label: {
              size: "2xs",
            },
          },
        },
      },
      Input: {
        variants: {
          outline: {
            field: {
              borderColor: "gray.300",
            },
            addon: {
              borderColor: "gray.300",
              bg: "none",
            },
          },
        },
        sizes: {
          md: {
            field: {
              height: "2.75rem",
              borderRadius: "sm",
            },
            addon: {
              height: "2.75rem",
              borderRadius: "sm",
              color: "gray.500",
            },
          },
        },
      },
      Select: {
        variants: {
          outline: {
            field: {
              borderColor: "gray.300",
            },
            icon: {
              color: "gray.500",
            },
          },
        },
        sizes: {
          md: {
            field: {
              height: "2.75rem",
              borderRadius: "sm",
            },
          },
        },
      },
      FormLabel: {
        baseStyle: {
          mb: 1,
          color: "gray.500",
          fontWeight: 400,
        },
      },
      Card: {
        baseStyle: {
          container: {
            borderRadius: "lg",
            bg: "white",
            borderWidth: 1,
            borderColor: "gray.200",
            boxShadow: "none",
          },
        },
      },
      Stat: {
        baseStyle: {
          container: {
            borderWidth: 1,
            borderRadius: "lg",
          },
          label: {
            p: 3,
            textTransform: "uppercase",
            borderBottomWidth: 1,
            color: "gray.500",
          },
          number: {
            p: 3,
            color: "gray.600",
          },
        },
        sizes: {
          md: {
            label: {
              fontSize: "xs",
            },
            number: {
              fontSize: "md",
              fontWeight: "bold",
            },
          },
        },
      },
      Table: {
        baseStyle: {
          th: {
            color: "gray.500",
            borderBottomWidth: 1,
          },
        },
        sizes: {
          md: {
            th: {
              px: 4,
              fontSize: "2xs",
              textTransform: "none",
              bg: "gray.50",
            },
            td: {
              px: 4,
              fontSize: "xs",
            },
          },
        },
      },
      Badge: {
        defaultProps: {
          size: "md",
          variant: "outline",
          colorScheme: "gray",
        },
        baseStyle: {
          borderRadius: "full",
          fontWeight: "normal",
          textTransform: "none",
        },
        variants: {
          outline: {
            borderWidth: 1,
            borderColor: "gray.200",
            shadow: "none",
          },
        },
        sizes: {
          md: {
            fontSize: "xs",
            px: 2,
          },
        },
      },
      Tag: {
        defaultProps: {
          size: "md",
          variant: "outline",
          colorScheme: "gray",
        },
        baseStyle: {
          container: {
            borderRadius: "full",
            fontWeight: "normal",
            textTransform: "none",
          },
        },
        variants: {
          outline: {
            container: {
              bg: "gray.50",
              borderWidth: 1,
              borderColor: "gray.200",
              shadow: "none",
              color: "black",
            },
          },
          active: {
            container: {
              bg: "green.50",
              borderWidth: 1,
              borderColor: "green.200",
              shadow: "none",
              color: "green",
            },
          },
          inactive: {
            container: {
              bg: "red.50",
              borderWidth: 1,
              borderColor: "red.200",
              shadow: "none",
              color: "red",
            },
          },
        },
        sizes: {
          md: {
            container: {
              fontSize: "xs",
              px: 2,
            },
          },
        },
      },
      Tabs: {
        baseStyle: {
          tab: {
            fontWeight: "semibold",
          },
        },
        variants: {
          line: {
            tab: {
              color: "gray.500",
              _selected: { color: "green.600", borderColor: "green.600" },
            },
          },
        },
        sizes: {
          md: {
            tab: {
              fontSize: "sm",
              px: 1,
              py: 2,
            },
            tablist: {
              gap: 3,
            },
            tabpanel: {
              px: 0,
              py: 6,
            },
          },
        },
      },
      Spinner: {
        baseStyle: {
          color: "green.500",
          display: "block",
          mx: "auto",
          my: 16,
        },
      },
      List: listTheme,
    },
    a: {
      color: "blue.500",
      textDecoration: "none",
      _hover: {
        textDecoration: "underline",
      },
    },
    styles: {
      global: {
        a: {
          color: "blue.500",
          textDecoration: "none",
          _hover: {
            textDecoration: "underline",
          },
        },
      },
    },
  },
  withDefaultColorScheme({ colorScheme: "decent-ec" }),
)

export default theme
