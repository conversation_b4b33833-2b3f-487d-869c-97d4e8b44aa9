import {
  Badge,
  Checkbox,
  CheckboxGroup,
  Table,
  TableContainer,
  Tag,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
} from "@chakra-ui/react"
import { Image } from "@chakra-ui/next-js"
import Pagination from "@/components/Pagination"
import React from "react"
import { ASSET_ICON_SIZE } from "@/constants"
import { Asset } from "@/app/types/assets"

type Props = {
  assets: Asset[]
  selectedAssets: string[]
  onChangeSelectedAssets: (selectedAssets: string[]) => void
}

const EnergyAssetsTable = ({
  assets,
  selectedAssets,
  onChangeSelectedAssets,
}: Props) => {
  const selectedAssetsCount = selectedAssets.length

  const handleChangeSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      onChangeSelectedAssets(assets.map(({ id }) => id))
    } else {
      onChangeSelectedAssets([])
    }
  }

  return (
    <>
      <TableContainer borderWidth={1} borderRadius="lg">
        <CheckboxGroup onChange={onChangeSelectedAssets} value={selectedAssets}>
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th colSpan={3}>
                  <Checkbox size="2xs" onChange={handleChangeSelectAll}>
                    {selectedAssetsCount} selected
                  </Checkbox>
                </Th>
                <Th>Consumption capacity</Th>
                <Th>Consumption profile</Th>
                <Th>Production capacity</Th>
                <Th>Production profile</Th>
                <Th>Status</Th>
              </Tr>
            </Thead>
            <Tbody>
              {assets.map(
                ({
                  id,
                  name,
                  consumptionCapacity,
                  consumptionProfile,
                  productionCapacity,
                  productionProfile,
                  status,
                }) => (
                  <Tr key={id}>
                    <Td maxW={5}>
                      <Checkbox value={id} />
                    </Td>
                    <Td maxW={ASSET_ICON_SIZE}>
                      <Image
                        src="/assets/assetIcons/consumer.svg"
                        alt="Consumer"
                        width={ASSET_ICON_SIZE}
                        height={ASSET_ICON_SIZE}
                        maxWidth={ASSET_ICON_SIZE}
                      />
                    </Td>
                    <Td pl={10} fontWeight={400}>
                      {name}
                    </Td>
                    <Td>
                      <Badge variant="outline" colorScheme="black" bg="gray.50">
                        {consumptionCapacity}
                      </Badge>
                    </Td>
                    <Td>CHART</Td>
                    <Td>
                      <Badge variant="outline" colorScheme="black" bg="gray.50">
                        {productionCapacity}
                      </Badge>
                    </Td>
                    <Td>CHART</Td>
                    <Td>
                      {status === "Active" ? (
                        <Tag variant="active">Active</Tag>
                      ) : (
                        <Tag variant="inactive">Inactive</Tag>
                      )}
                    </Td>
                  </Tr>
                ),
              )}
            </Tbody>
          </Table>
        </CheckboxGroup>
      </TableContainer>
      <Pagination totalPages={10} currentPage={2} onChangePage={() => {}} />
    </>
  )
}

export default EnergyAssetsTable
