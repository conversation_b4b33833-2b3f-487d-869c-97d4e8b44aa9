"use client"

import Fieldset from "@/components/form/Fieldset"
import { <PERSON><PERSON>, <PERSON>rid, <PERSON>rid<PERSON><PERSON>, <PERSON>ack } from "@chakra-ui/react"
import React from "react"
import Input from "@/components/form/Input"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodR<PERSON>olver } from "@hookform/resolvers/zod"
import Select from "@/components/form/Select"
import RadioCards from "@/components/form/RadioCards"
import LocationInput from "@/components/form/LocationInput"
import { Feature, Point } from "geojson"
import FileUpload from "@/components/form/FileUpload"
import PageHeader from "@/components/PageHeader"
import {
  ACTIVITIES,
  ASSOCIATIONS,
  BOARD_MEMBERS,
  CHALLENGES_IN_ESTABLISHING_THE_ENERGY_COMMUNITY,
  CHALLENGES_IN_THE_LAST_YEAR,
  CITIZENS_INDIVIDUAL,
  COUNTRY,
  ENERGY_COMMUNITY_TYPE,
  HOW_DO_YOU_INVOLVE_MEMBERS_IN_DECISION_MAKING,
  LANGUAGE,
  MUN<PERSON><PERSON><PERSON><PERSON>IE<PERSON>,
  <PERSON>THER,
  OWNERSHIP_STRUCTURE,
  <PERSON>RT<PERSON><PERSON>ATION_IN_LAST_YEARS_GENERAL_ASSEMBLY,
  SERVICES,
  SHARE_OF_FEMALE_MEMBERS,
  SHARE_OF_MEMBERS_UNDER_35,
  SHARE_OF_VULNERABLE_MEMBERS,
  SMES,
  STATUS,
  TSO,
  TYPES_OF_MEMBERSHIP,
  VOTING_SYSTEM,
} from "@/data/ecProfileOptions"
import { ECProfileFormValues } from "@/app/types/ecProfile"
import { putCommunityUI } from "@/services/api"
import generateCognitoCSS from "@/services/cognito/generateCognitoCSS"
import ecProfileColorThemes from "@/data/ecProfileColorThemes"
import ThemeRadioCard from "./components/ThemeRadioCard"

type FormValues = ECProfileFormValues

const schema = z.object({
  nameOfEC: z.string().optional(),
  location: z
    .custom<
      Feature<Point>
    >((data) => (data as Feature<Point> | undefined)?.type === "Feature")
    .optional(),
  logo: z.instanceof(File).optional(),
  myDomain: z.string().optional(),
  language: z.string().optional(),
  country: z.string().optional(),
  tso: z.string().optional(),
  energyCommunityType: z.string().optional(),
  typesOfMembership: z.string().optional(),
  theme: z.string().optional(),
  numberOfMembers: z.string().optional(),
  citizensIndividual: z.string().optional(),
  SMEs: z.string().optional(),
  municipalities: z.string().optional(),
  associations: z.string().optional(),
  other: z.string().optional(),
  website: z.string().optional(),
  status: z.string().optional(),
  activities: z.string().optional(),
  services: z.string().optional(),
  challengesInEstablishingTheEnergyCommunity: z.string().optional(),
  challengesInTheLastYear: z.string().optional(),
  ownershipStructure: z.string().optional(),
  shareOfFemaleMembers: z.string().optional(),
  shareOfVulnerableMembers: z.string().optional(),
  shareOfMembersUnder35: z.string().optional(),
  howDoYouInvolveMembersInDecisionMaking: z.string().optional(),
  boardMembers: z.string().optional(),
  votingSystem: z.string().optional(),
  participationInLastYearsGeneralAssembly: z.string().optional(),
})
const Page = () => {
  const {
    handleSubmit,
    register,
    formState: { isValid, isSubmitting, errors },
    control,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
  })

  const onSubmit = async (values: FormValues) => {
    const { logo, theme } = values
    const selectedTheme = ecProfileColorThemes.find((t) => t.value === theme)
    if (!selectedTheme) {
      return
    }

    try {
      //await postEcProfile(values)
      await putCommunityUI(logo, generateCognitoCSS(selectedTheme))
    } catch (error) {
      console.error("error", error)
    }
  }

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <PageHeader
          title="EC profile"
          description="Set up or edit your EC profile"
          actions={
            <Button type="submit" size="lg" isLoading={isSubmitting}>
              Save
            </Button>
          }
        />
        <Stack spacing={6}>
          <Fieldset name="Basic details">
            <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
              <GridItem colSpan={2}>
                <Input label="Name of EC" {...register("nameOfEC")} />
              </GridItem>
              <GridItem>
                <Input
                  label="My domain"
                  rightAddon=".decent.ec"
                  {...register("myDomain")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Language"
                  options={LANGUAGE}
                  {...register("language")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Country"
                  options={COUNTRY}
                  {...register("country")}
                />
              </GridItem>
              <GridItem>
                <Select label="TSO" options={TSO} {...register("tso")} />
              </GridItem>
              <GridItem>
                <Select
                  label="Energy community type"
                  options={ENERGY_COMMUNITY_TYPE}
                  {...register("energyCommunityType")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Types of membership"
                  options={TYPES_OF_MEMBERSHIP}
                  {...register("typesOfMembership")}
                />
              </GridItem>
              <GridItem colSpan={2}>
                <RadioCards
                  label="Choose theme"
                  options={ecProfileColorThemes}
                  CardComponent={ThemeRadioCard}
                  name="theme"
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={2}>
                <LocationInput
                  label="Location"
                  name="location"
                  control={control}
                />
              </GridItem>
              <GridItem colSpan={2}>
                <FileUpload
                  label="Logo (optional)"
                  name="logo"
                  control={control}
                  description="SVG, PNG, JPG or GIF (max. 800x400px)"
                />
              </GridItem>
            </Grid>
          </Fieldset>
          <Fieldset name="Overall information">
            <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
              <GridItem>
                <Input
                  label="Number of members"
                  {...register("numberOfMembers")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Citizens/Individual"
                  options={CITIZENS_INDIVIDUAL}
                  {...register("citizensIndividual")}
                />
              </GridItem>
              <GridItem>
                <Select label="SMEs" options={SMES} {...register("SMEs")} />
              </GridItem>
              <GridItem>
                <Select
                  label="Municipalities"
                  options={MUNICIPALITIES}
                  {...register("municipalities")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Associations"
                  options={ASSOCIATIONS}
                  {...register("associations")}
                />
              </GridItem>
              <GridItem>
                <Select label="Other" {...register("other")} options={OTHER} />
              </GridItem>
              <GridItem>
                <Input label="Website" {...register("website")} />
              </GridItem>
              <GridItem>
                <Select
                  label="Status"
                  options={STATUS}
                  {...register("status")}
                />
              </GridItem>
            </Grid>
          </Fieldset>
          <Fieldset name="Activities">
            <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
              <GridItem>
                <Select
                  label="Activities"
                  options={ACTIVITIES}
                  {...register("activities")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Services"
                  options={SERVICES}
                  {...register("services")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Challenges in establishing the energy community"
                  options={CHALLENGES_IN_ESTABLISHING_THE_ENERGY_COMMUNITY}
                  {...register("challengesInEstablishingTheEnergyCommunity")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Challenges in the last year"
                  options={CHALLENGES_IN_THE_LAST_YEAR}
                  {...register("challengesInTheLastYear")}
                />
              </GridItem>
            </Grid>
          </Fieldset>
          <Fieldset name="Governance">
            <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
              <GridItem>
                <Select
                  label="Ownership structure"
                  options={OWNERSHIP_STRUCTURE}
                  {...register("ownershipStructure")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Share of female members"
                  options={SHARE_OF_FEMALE_MEMBERS}
                  {...register("shareOfFemaleMembers")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Share of vulnerable members"
                  options={SHARE_OF_VULNERABLE_MEMBERS}
                  {...register("shareOfVulnerableMembers")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Share of members under 35"
                  options={SHARE_OF_MEMBERS_UNDER_35}
                  {...register("shareOfMembersUnder35")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="How do you involve members in decision-making?"
                  options={HOW_DO_YOU_INVOLVE_MEMBERS_IN_DECISION_MAKING}
                  {...register("howDoYouInvolveMembersInDecisionMaking")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Board members"
                  options={BOARD_MEMBERS}
                  {...register("boardMembers")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Voting system"
                  options={VOTING_SYSTEM}
                  {...register("votingSystem")}
                />
              </GridItem>
              <GridItem>
                <Select
                  label="Participation in last year's General Assembly"
                  options={PARTICIPATION_IN_LAST_YEARS_GENERAL_ASSEMBLY}
                  {...register("participationInLastYearsGeneralAssembly")}
                />
              </GridItem>
            </Grid>
          </Fieldset>
        </Stack>
      </form>
    </>
  )
}

export default Page
