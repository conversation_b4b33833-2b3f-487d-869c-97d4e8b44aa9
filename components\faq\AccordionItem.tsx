import React, { ReactNode } from "react"
import { AccordionItem as ChakraAccordionItem } from "@chakra-ui/react"
import {
  AccordionButton,
  AccordionPanel,
  Flex,
  Heading,
  Stack,
} from "@chakra-ui/react"
import { Image } from "@chakra-ui/next-js"

type Props = Readonly<{
  title: string
  children: string | ReactNode | ReactNode[]
}>

const AccordionItem = ({ title, children }: Props) => (
  <ChakraAccordionItem
    borderTopWidth={1}
    _first={{ borderTopWidth: 0 }}
    _last={{ borderBottomWidth: 0 }}
  >
    {/* todo: check if we can use another kind of Image component or Icon component and remove "use client" */}
    {({ isExpanded }) => (
      <>
        <Heading as="h4">
          <AccordionButton>
            <Flex
              fontSize="lg"
              fontWeight={600}
              justify="space-between"
              w="full"
              py={4}
            >
              {title}
              {isExpanded ? (
                <Image
                  src="/assets/icons/minus-circle.svg"
                  width={5}
                  height={5}
                  alt="Collapse"
                />
              ) : (
                <Image
                  src="/assets/icons/plus-circle.svg"
                  width={5}
                  height={5}
                  alt="Expand"
                />
              )}
            </Flex>
          </AccordionButton>
        </Heading>
        <AccordionPanel color="gray.400" pt={0} px={4}>
          <Stack>{children}</Stack>
        </AccordionPanel>
      </>
    )}
  </ChakraAccordionItem>
)

export default AccordionItem
