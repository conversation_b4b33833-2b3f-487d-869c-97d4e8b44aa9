"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Flex, GridItem, Heading, Grid, Button, Stack } from "@chakra-ui/react"
import PrimarySection from "@/components/PrimarySection"
import Input from "@/components/form/Input"
import Checkbox from "@/components/form/Checkbox"
import useKiltSessionStore from "@/hooks/useKiltSessionStore"
import { requestKiltAttestationFromSporran } from "@/helpers/kilt"
import useAuthenticationStore from "@/hooks/useAuthenticationStore"
import { useRouter } from "next/navigation"
import useToast from "@/hooks/useToast"
import SignUpModal from "@/components/SignUpModal"
import { useState } from "react"
import MissingSporranOverlay from "@/components/missingSporran/MissingSporranOverlay"
import MissingSporranModal from "@/components/missingSporran/MissingSporranModal"
import { DEFAULT_AUTH_ROUTE } from "@/constants"

type FormValues = {
  firstName: string
  lastName: string
  email: string
  acceptTerms: boolean
}

const schema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  acceptTerms: z.boolean().refine((value) => value),
})

const Page = () => {
  const toast = useToast()
  const [missingSporranModalIsOpen, setMissingSporranModalIsOpen] = useState(false)
  const router = useRouter()
  const { setLoggedIn } = useAuthenticationStore()
  const [attestationRequestIsProcessing, setAttestationRequestIsProcessing] = useState(false)
  const { session, sessionIsLoading } = useKiltSessionStore((state) => ({
    session: state.session,
    sessionIsLoading: state.sessionIsLoading,
  }))

  const {
    handleSubmit,
    register,
    formState: { isValid, isSubmitting },
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
  })

  const startAttestationRequestProcessing = () => setAttestationRequestIsProcessing(true)

  const onSubmit = async ({ firstName, lastName, email }: FormValues) => {
    if (!session) {
      setMissingSporranModalIsOpen(true)
      return
    }

    try {
      await requestKiltAttestationFromSporran(
        firstName,
        lastName,
        email,
        session,
        startAttestationRequestProcessing,
      )
      toast({
        description:
          "We have successfully created a KILT credential for you! You can find it in your Sporran wallet to use it for signing in to Decent.ec.",
        duration: 15000,
      })
      setLoggedIn(true)
      router.push(DEFAULT_AUTH_ROUTE)
    } catch (error) {
      toast({
        title: "Error requesting  KILT credential from Sporran.",
        description: "Something went wrong. Please try again.",
        status: "error",
      })
      console.error("Error requesting KILT credential from Sporran:", error)
    } finally {
      setAttestationRequestIsProcessing(false)
    }
  }

  return (
    <>
      <PrimarySection justify="center">
        <Flex maxW={560} direction="column" gap={16}>
          <Heading as="h1" textAlign="center">
            Plan, organise and manage an energy community!
          </Heading>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Flex
              direction="column"
              align="center"
              bg="white"
              borderWidth={1}
              borderRadius="lg"
              gap={2}
              overflow="hidden"
            >
              <Heading fontSize="2xl" p={6}>
                Register
              </Heading>
              <Stack pos="relative" gap={14} p={6}>
                <Grid templateColumns="repeat(2, 1fr)" gap={4} w="full">
                  <GridItem>
                    <Input label="First name" {...register("firstName")} />
                  </GridItem>
                  <GridItem>
                    <Input label="Last name" {...register("lastName")} />
                  </GridItem>
                  <GridItem colSpan={2}>
                    <Input label="E-mail address" {...register("email")} />
                  </GridItem>
                  <GridItem colSpan={2}>
                    <Checkbox
                      label="I accept the Terms & Conditions"
                      {...register("acceptTerms")}
                    />
                  </GridItem>
                </Grid>
                <Flex direction="column" align="center" gap={4}>
                  <Button
                    type="submit"
                    isDisabled={!isValid}
                    isLoading={isSubmitting}
                  >
                    Get Started
                  </Button>
                </Flex>
                {!sessionIsLoading && !session && (
                  <MissingSporranOverlay />
                )}
              </Stack>
            </Flex>
          </form>
        </Flex>
      </PrimarySection>
      <SignUpModal isOpen={isSubmitting} isLoading={attestationRequestIsProcessing} />
      <MissingSporranModal
        isOpen={missingSporranModalIsOpen}
        onClose={() => setMissingSporranModalIsOpen(false)}
      />
    </>
  )
}

export default Page
