import { startImportJob, stopImportJob } from "@/services/cognito/cognito"
import { ImportJobStatusChange } from "@/app/types/community"
import cognito from "@/data/cognito"

type Props = {
  request: Request
  params: Promise<{ jobId: string }>
}

export async function PATCH({ request, params }: Props) {
  const { jobId } = await params

  const { changeStatus }: { changeStatus: ImportJobStatusChange } =
    await request.json()
  try {
    await (changeStatus === "start" ? startImportJob : stopImportJob)(
      cognito.userPoolId,
      jobId,
    )

    return Response.json(
      {
        message: `Import job ${changeStatus === "start" ? "started" : "stopped"} successfully.`,
      },
      { status: 200 },
    )
  } catch (error) {
    return Response.json({ error }, { status: 500 })
  }
}
