import { defineRouting } from "next-intl/routing"
import { localesWithCountryNames } from "./i18n"

export const routing = defineRouting({
  locales: localesWithCountryNames.map(({ code }) => code),
  defaultLocale: "en",
  localePrefix: "always",
  pathnames: {
    "/": "/",
    "/registration": "/registration",
    "/faq/how-to-start-an-energy-community": {
      en: "/faq/how-to-start-an-energy-community",
      hu: "/faq/hogyan-inditsunk-egy-energiakozosseget",
    },
    "/faq/how-can-you-contribute-with-self-consumption-and-collectively": {
      en: "/faq/how-can-you-contribute-with-self-consumption-and-collectively",
      hu: "/faq/hogyan-tudsz-hozzajarulni-onfogyasztassal",
    },
    "/faq/how-do-electric-charging-points-work-in-energy-communities": {
      en: "/faq/how-do-electric-charging-points-work-in-energy-communities",
      hu: "/faq/hogyan-mukodnek-az-elektromos-toltopontok-az-energiakozossegekben",
    },
    "/faq/how-to-achieve-100-percent-renewable-energy-generation": {
      en: "/faq/how-to-achieve-100-percent-renewable-energy-generation",
      hu: "/faq/hogyan-lehet-elerni-a-100-szazalekos-megujulo-energiatermelest",
    },
    "/faq/how-to-implement-shared-electric-mobility": {
      en: "/faq/how-to-implement-shared-electric-mobility",
      hu: "/faq/hogyan-valosithato-meg-az-elektromos-mobilitas",
    },
    "/faq/what-are-energy-communities": {
      en: "/faq/what-are-energy-communities",
      hu: "/faq/mik-az-energiakozossegek",
    },
    "/faq/which-legal-form-to-choose-for-an-energy-community": {
      en: "/faq/which-legal-form-to-choose-for-an-energy-community",
      hu: "/faq/milyen-jogi-format-valasszunk-egy-energiakozosseg-szamara",
    },
  }
})

// Export individual properties for backward compatibility
export const { locales, defaultLocale, localePrefix, pathnames } = routing
