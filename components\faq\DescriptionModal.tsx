import {
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  ModalOverlay,
} from "@chakra-ui/react"
import DESCRIPTIONS from "@/components/faq/descriptions"
import React from "react"

type Props = Readonly<{
  activeDescription: string | null
  onClose: () => void
  onClickDescriptionLink: (key: string) => void
  locale: string
}>

const DescriptionModal = ({
  activeDescription,
  onClose,
  onClickDescriptionLink,
  locale,
}: Props) => (
  <Modal isOpen={!!activeDescription} onClose={onClose} isCentered>
    <ModalOverlay />
    <ModalContent>
      <ModalCloseButton />
      <ModalBody p={8}>
        {activeDescription &&
          DESCRIPTIONS({ onClickDescriptionLink, locale })[activeDescription]}
      </ModalBody>
    </ModalContent>
  </Modal>
)

export default DescriptionModal
