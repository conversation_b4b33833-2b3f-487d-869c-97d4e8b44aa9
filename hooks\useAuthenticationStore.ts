import { create, Store<PERSON><PERSON>, UseBoundStore } from "zustand"
import { isLoggedIn } from "@/helpers/kilt"

type AuthenticationStore = {
  loggedIn: boolean
  setLoggedIn: (loggedIn: boolean) => void
}

const useAuthenticationStore: UseBoundStore<StoreApi<AuthenticationStore>> =
  create((set) => ({
    loggedIn: false,
    setLoggedIn: (loggedIn: boolean) => set(() => ({ loggedIn })),
  }))

export default useAuthenticationStore
