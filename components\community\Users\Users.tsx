"use client"

import {
  Box,
  Button,
  Flex,
  Heading,
  Radio,
  RadioGroup,
  SimpleGrid,
  Spinner,
  Stack,
  Table,
  TableContainer,
  Tag,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  Card,
  CardBody,
  CardHeader,
} from "@chakra-ui/react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/md"
import Select from "@/components/form/Select"
import Input from "@/components/form/Input"
import React, { useCallback, useEffect } from "react"
import Pagination from "@/components/Pagination"
import { User } from "@/app/types/community"
import { USER_ATTRIBUTES } from "@/services/cognito/cognito"
import { deletePoolUser, getPoolUsers } from "@/services/api"
import useToast from "@/hooks/useToast"
import AddUser from "./AddUser"
import { debounce } from "lodash"

const Users = () => {
  const toast = useToast()
  const [selectedUser, setSelectedUser] = React.useState<string>()
  const [users, setUsers] = React.useState<User[]>([])
  const [isDeleting, setIsDeleting] = React.useState<boolean>(false)
  const [isLoading, setIsLoading] = React.useState<boolean>(true)

  const handleClickDeleteUser = async () => {
    setIsDeleting(true)
    if (selectedUser) {
      await deletePoolUser(selectedUser)
      toast({
        description: `You have successfully deleted the user ${selectedUser}.`,
      })

      setUsers(users.filter((user) => user.id !== selectedUser))
      setSelectedUser(undefined)
    }
    setIsDeleting(false)

    return
  }

  const [currentPage, setCurrentPage] = React.useState<number>(1)

  const filterOptions = Object.entries(USER_ATTRIBUTES).map(
    ([value, label]) => ({
      label,
      value,
    }),
  )
  const [filterProperty, setFilterProperty] = React.useState<string>(
    filterOptions[0].value,
  )

  const [filterValue, setFilterValue] = React.useState<string>("")

  const resultsPerPage = 10
  const totalPages = Math.ceil(users.length / resultsPerPage)
  const usersOfCurrentPage = users.slice(
    (currentPage - 1) * resultsPerPage,
    currentPage * resultsPerPage,
  )

  const debouncedGetPoolUsers = useCallback(
    debounce((params) => {
      setIsLoading(true)
      getPoolUsers(params).then((poolUsers) => {
        setUsers(poolUsers)
        setIsLoading(false)
      })
    }, 500),
    [],
  )

  useEffect(
    () => debouncedGetPoolUsers({ filterValue, filterProperty }),
    [filterValue, filterProperty, debouncedGetPoolUsers],
  )

  const handleChangeSelectedUser = (id: string) => {
    setSelectedUser(id)
  }

  const handleUserAdded = (user: User) => {
    setUsers([user, ...users])
  }

  if (users.length === 0 && isLoading) {
    return <Spinner />
  }

  return (
    <Card>
      <CardHeader borderBottomWidth={1} mb={4}>
        <Flex align="center" justify="space-between">
          <Box w="60%">
            <Heading size="sm">Users</Heading>
            <Text fontSize="sm" color="gray.500">
              View, edit, and create users in your pool. Users that are enabled
              and confirmed can sign in to your user pool.
            </Text>
          </Box>
          <Flex gap={4}>
            <Button
              variant="gray-outline"
              onClick={handleClickDeleteUser}
              isDisabled={!selectedUser}
              isLoading={isDeleting}
            >
              Delete selected user {selectedUser && <>({selectedUser})</>}
            </Button>
            <AddUser onUserAdded={handleUserAdded} />
          </Flex>
        </Flex>
      </CardHeader>
      <CardBody pt={0}>
        <Stack spacing={6}>
          <SimpleGrid columns={2} gap={4}>
            <Select
              label="Property"
              options={filterOptions}
              value={filterProperty}
              onChange={(e) => setFilterProperty(e.target.value)}
            />
            <Input
              label="Search"
              value={filterValue}
              onChange={(e) => setFilterValue(e.target.value)}
            />
          </SimpleGrid>
          <TableContainer borderWidth={1} borderRadius="lg">
            <RadioGroup
              onChange={handleChangeSelectedUser}
              value={selectedUser}
            >
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th />
                    <Th>User name</Th>
                    <Th>Email address</Th>
                    <Th>Confirmation status</Th>
                    <Th>Status</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {usersOfCurrentPage.map(
                    ({ id, username, email, confirmation_status, status }) => (
                      <Tr key={id}>
                        <Td maxW={2}>
                          <Radio value={id} />
                        </Td>
                        <Td>{username}</Td>
                        <Td>{email}</Td>
                        <Td>
                          {confirmation_status ? (
                            <Tag variant="active" gap={1}>
                              <MdCheck /> Confirmed
                            </Tag>
                          ) : (
                            <Tag>Unconfirmed</Tag>
                          )}
                        </Td>
                        <Td>{status ? "Enabled" : "Disabled"}</Td>
                      </Tr>
                    ),
                  )}
                </Tbody>
              </Table>
            </RadioGroup>
          </TableContainer>
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            onChangePage={setCurrentPage}
          />
        </Stack>
      </CardBody>
    </Card>
  )
}

export default Users
