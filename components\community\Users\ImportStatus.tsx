import { UserImportJobStatusType } from "@aws-sdk/client-cognito-identity-provider"
import { Box, Tag } from "@chakra-ui/react"
import { Md<PERSON>heck, MdClear } from "react-icons/md"
import { IconType } from "react-icons"

type Props = {
  status?: UserImportJobStatusType
}

type ImportStatusType = {
  label: string
  variant: string
  icon?: IconType
}

const IMPORT_STATUSES: Record<string, ImportStatusType> = {
  Created: { label: "Created", variant: "outline" },
  Pending: { label: "Pending", variant: "outline" },
  InProgress: { label: "In progress", variant: "outline" },
  Stopping: { label: "Stopping", variant: "outline" },
  Expired: { label: "Expired", variant: "outline" },
  Stopped: { label: "Stopped", variant: "outline" },
  Succeeded: { label: "Succeeded", variant: "active", icon: MdCheck },
  Failed: { label: "Failed", variant: "inactive", icon: MdClear },
}

const ImportStatus = ({ status }: Props) => {
  if (!status) {
    return <Tag>Unknown</Tag>
  }

  const { variant, label, icon: Icon } = IMPORT_STATUSES[status]

  return (
    <Tag variant={variant}>
      {Icon && (
        <Box mr={1}>
          <Icon />
        </Box>
      )}
      {label}
    </Tag>
  )
}

export default ImportStatus
