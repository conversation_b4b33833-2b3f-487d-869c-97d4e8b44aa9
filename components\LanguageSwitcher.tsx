import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>uI<PERSON> } from "@chakra-ui/react"
import { usePathname, useRouter } from "next/navigation"
import { extractLanguageCodeFromPathname } from "@/helpers/language"
import { localesWithCountryNames } from "@/i18n/i18n"

const LanguageSwitcher = () => {
  const router = useRouter()
  const pathname = usePathname()

  const languageCode = extractLanguageCodeFromPathname(pathname)
  const selectedLanguage = localesWithCountryNames.find(
    ({ code }) => code === languageCode,
  )

  const handleLanguageChange = (newLang: string) => {
    // Replace the current language code with the new one
    const newPathname = pathname.replace(/^\/\w{2}/, `/${newLang}`)

    router.push(newPathname)
  }

  return (
    <Menu autoSelect={false}>
      <MenuButton as={Button} rightIcon={<span>▼</span>}>
        {selectedLanguage?.country}
      </MenuButton>
      <MenuList>
        {localesWithCountryNames.map(({ country, code }) => (
          <MenuItem key={code} onClick={() => handleLanguageChange(code)}>
            {country}
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  )
}

export default LanguageSwitcher
