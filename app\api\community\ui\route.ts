import { setUICustomization } from "@/services/cognito/cognito"
import cognito from "@/data/cognito"

export async function PUT(request: Request) {
  const formData = await request.formData()
  const logo = formData.get("logo") as File
  const css = formData.get("css") as string

  await setUICustomization(cognito.userPoolId, cognito.clientId, css, logo)

  return Response.json(
    { message: "UI customization files uploaded successfully." },
    { status: 200 },
  )
}
