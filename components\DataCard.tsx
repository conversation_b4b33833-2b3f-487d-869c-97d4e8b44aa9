"use client"

import {
  <PERSON>,
  <PERSON>Header,
  <PERSON>ing,
  SimpleGrid,
  Text,
  Card,
  CardBody,
} from "@chakra-ui/react"
import { ReactNode } from "react"
import { MdContentCopy } from "react-icons/md"
import CopyToClipboard from "@/components/CopyToClipboard"

type Props = {
  title: string
  children: ReactNode
}

const DataCard = ({ title, children }: Props) => (
  <Card>
    <CardHeader>
      <Heading size="sm">{title}</Heading>
    </CardHeader>
    <CardBody pt={0}>
      <SimpleGrid columns={3} spacing={4}>
        {children}
      </SimpleGrid>
    </CardBody>
  </Card>
)

type DataFieldProps = {
  label: string
  value: string | number
  copiable?: boolean
}

export const DataField = ({ label, value, copiable }: DataFieldProps) => (
  <Box>
    <Heading size="sm" fontWeight="normal" color="gray.500">
      {label}
    </Heading>
    <Text pt="1" fontSize="md" fontWeight="500">
      {copiable && (
        <CopyToClipboard text={value.toString()}>
          <Text color="gray.500">
            <MdContentCopy cursor="pointer" size={20} />
          </Text>
        </CopyToClipboard>
      )}
      {value}
    </Text>
  </Box>
)

export default DataCard
