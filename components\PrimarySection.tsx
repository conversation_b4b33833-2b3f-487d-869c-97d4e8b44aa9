import { Flex, FlexProps } from "@chakra-ui/react"
import React, { ReactNode } from "react"

type Props = {
  children: ReactNode
  hideBackgroundImage?: boolean
} & FlexProps

const PrimarySection = ({ children, ...props }: Props) => (
  <Flex
    direction="column"
    align="center"
    bgImage="/assets/background.svg"
    w="full"
    minH="calc(100dvh - 4.75rem)"
    bgRepeat="no-repeat"
    bgSize="100%"
    bgPosition="center bottom"
  >
    <Flex w="full" maxW={1200} px={6} py={16} {...props}>
      {children}
    </Flex>
  </Flex>
)

export default PrimarySection
