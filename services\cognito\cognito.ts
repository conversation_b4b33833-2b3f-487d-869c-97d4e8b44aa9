import {
  AdminCreate<PERSON>serCommand,
  AdminCreate<PERSON>ser<PERSON>ommandOutput,
  AdminDeleteUserCommand,
  CognitoIdentityProviderClient,
  CreateUserImportJobCommand,
  DescribeUserPoolCommand,
  ListUserImportJobsCommand,
  paginateListUsers,
  SetUICustomizationCommand,
  StartUserImportJobCommand,
  StopUserImportJobCommand,
  UserImportJobType,
  UserType,
} from "@aws-sdk/client-cognito-identity-provider"
import {
  ImportJob,
  ImportJobStartAction,
  User,
  UserPool,
} from "@/app/types/community"

export const getPool = async (userPoolId: string): Promise<UserPool> => {
  const client = new CognitoIdentityProviderClient()
  const command = new DescribeUserPoolCommand({
    UserPoolId: userPoolId,
  })
  const response = await client.send(command)

  return {
    poolId: response.UserPool?.Id ?? "",
    arn: response.UserPool?.Arn ?? "",
    name: response.UserPool?.Name ?? "",
    userCount: response.UserPool?.EstimatedNumberOfUsers ?? 0,
    createdAt: response.UserPool?.CreationDate ?? new Date(),
    updatedAt: response.UserPool?.LastModifiedDate ?? new Date(),
  }
}

export const extractPoolUser = (user: UserType): User => ({
  id: user.Username ?? "",
  username: user.Username ?? "",
  email: user.Attributes?.find((attr) => attr.Name === "email")?.Value ?? "",
  confirmation_status: user.UserStatus === "CONFIRMED",
  status: user.Enabled ?? false,
})

export const getPoolUsers = async (
  userPoolId: string,
  filter: string = "",
): Promise<User[]> => {
  const client = new CognitoIdentityProviderClient()

  const users = []
  for await (const page of paginateListUsers(
    { client },
    { UserPoolId: userPoolId, Limit: 60, Filter: filter || "" },
  )) {
    users.push(...(page.Users ?? []))
  }

  return users
    .sort((a, b) =>
      (a.UserCreateDate ?? new Date()) > (b.UserCreateDate ?? new Date())
        ? -1
        : 1,
    )
    .map(extractPoolUser)
}

export const createPoolUser = async (
  userPoolId: string,
  username: string,
  email: string,
): Promise<AdminCreateUserCommandOutput> => {
  const client = new CognitoIdentityProviderClient()
  const command = new AdminCreateUserCommand({
    UserPoolId: userPoolId,
    Username: username,
    UserAttributes: [
      {
        Name: "email",
        Value: email,
      },
    ],
  })

  return client.send(command)
}

export const deletePoolUser = async (
  userPoolId: string,
  username: string,
): Promise<void> => {
  const client = new CognitoIdentityProviderClient()
  const command = new AdminDeleteUserCommand({
    UserPoolId: userPoolId,
    Username: username,
  })

  await client.send(command)
}

export const extractImportJob = (importJob: UserImportJobType): ImportJob => ({
  id: importJob.JobId ?? "",
  name: importJob.JobName ?? "",
  importedUsers: importJob.ImportedUsers ?? 0,
  skippedUsers: importJob.SkippedUsers ?? 0,
  failedUsers: importJob.FailedUsers ?? 0,
  createdAt: importJob.CreationDate ?? new Date(),
  status: importJob.Status,
})

export const getImportJobs = async (
  userPoolId: string,
): Promise<ImportJob[]> => {
  const client = new CognitoIdentityProviderClient()

  const importJobs = []
  let hasNext = true
  let paginationToken = undefined

  while (hasNext) {
    const command: ListUserImportJobsCommand = new ListUserImportJobsCommand({
      MaxResults: 60,
      UserPoolId: userPoolId,
      PaginationToken: paginationToken,
    })

    const response = await client.send(command)
    importJobs.push(...(response.UserImportJobs ?? []))

    paginationToken = response.PaginationToken
    hasNext = !!paginationToken
  }

  return importJobs.map(extractImportJob) ?? []
}

export const postImportJob = async (
  cloudWatchLogsRoleArn: string,
  jobName: string,
  userPoolId: string,
  csvFile: File,
  action: ImportJobStartAction,
): Promise<UserImportJobType> => {
  const client = new CognitoIdentityProviderClient()
  const command = new CreateUserImportJobCommand({
    CloudWatchLogsRoleArn: cloudWatchLogsRoleArn,
    JobName: jobName,
    UserPoolId: userPoolId,
  })
  const createUserImportJobResponse = await client.send(command)
  const job = createUserImportJobResponse.UserImportJob

  if (!job) {
    throw new Error("Failed to create import job.")
  }

  if (!job.PreSignedUrl) {
    throw new Error("Pre-signed URL not found.")
  }

  if (!job.JobId) {
    throw new Error("Job ID not found.")
  }

  const fileUploadResponse = await fetch(job.PreSignedUrl, {
    method: "PUT",
    headers: {
      "Content-Type": "text/csv",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Credentials": "true",
      "x-amz-server-side-encryption": "aws:kms",
    },
    body: csvFile,
  })

  if (!fileUploadResponse.ok) {
    throw new Error("Failed to upload file.")
  }

  if (action === "createAndStart") {
    await startImportJob(userPoolId, job.JobId)
  }

  return job
}

export const startImportJob = async (
  userPoolId: string,
  jobId: string,
): Promise<void> => {
  const client = new CognitoIdentityProviderClient()
  const command = new StartUserImportJobCommand({
    JobId: jobId,
    UserPoolId: userPoolId,
  })

  await client.send(command)
}

export const stopImportJob = async (
  userPoolId: string,
  jobId: string,
): Promise<void> => {
  const client = new CognitoIdentityProviderClient()
  const command = new StopUserImportJobCommand({
    JobId: jobId,
    UserPoolId: userPoolId,
  })

  await client.send(command)
}

export const setUICustomization = async (
  userPoolId: string,
  clientId: string,
  css: string,
  imageFile: File,
): Promise<void> => {
  const client = new CognitoIdentityProviderClient()
  const command = new SetUICustomizationCommand({
    UserPoolId: userPoolId,
    ClientId: clientId,
    CSS: css,
    ImageFile: new Uint8Array(await imageFile.arrayBuffer()),
  })

  await client.send(command)
}

export const USER_ATTRIBUTES: Record<string, string> = {
  username: "User name",
  email: "Email address",
  phone_number: "Phone number",
  name: "Name",
  given_name: "First name",
  family_name: "Last name",
  preferred_username: "Preferred username",
  birthdate: "Birth date",
  sub: "User ID (Sub)",
  confirmation_status: "Confirmation status",
  status: "Status",
}
