import { Box, Heading, ListItem, Text, UnorderedList } from "@chakra-ui/react"
import { useTranslations } from "next-intl"
import React from "react"

type Props = {
  title: string
  subtitle: string
  pros: string[]
  cons: string[]
}

const ParagraphWithProsAndCons = ({ title, subtitle, pros, cons }: Props) => {
  const t = useTranslations("FAQ")

  return (
    <>
      <Heading as="h4" size="md">
        {title}
      </Heading>
      <Text>{subtitle}</Text>
      <Box>
        <Text mt={4} mb={2}>
          {t("prosTitle")}:
        </Text>
        <UnorderedList>
          {pros.map((pro) => (
            <ListItem key={pro}>{pro}</ListItem>
          ))}
        </UnorderedList>
      </Box>
      <Box>
        <Text mt={4} mb={2}>
          {t("consTitle")}:
        </Text>
        <UnorderedList>
          {cons.map((con) => (
            <ListItem key={con}>{con}</ListItem>
          ))}
        </UnorderedList>
      </Box>
    </>
  )
}

export default ParagraphWithProsAndCons
