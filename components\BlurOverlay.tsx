import { <PERSON>ackProps, Stack, Box } from "@chakra-ui/react"

export default function BlurOverlay({ ...props }: StackProps ) {
  return (
    <Stack
      position="absolute"
      top={0}
      left={0}
      right={0}
      bottom={0}
      p={6}
      backdropFilter="auto"
      backdropBlur="sm"
      {...props}
    >
      {props.children}
      <Box position="absolute" top={0} left={0} right={0} bottom={0} bg="whiteAlpha.700" zIndex={-1} />
    </Stack>
  )
}
