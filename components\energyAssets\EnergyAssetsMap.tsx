import Map, { Mark<PERSON> } from "react-map-gl"
import configuration from "@/configuration"
import React from "react"
import { Box, Flex, Text } from "@chakra-ui/react"
import { ASSET_ICON_SIZE } from "@/constants"
import { Image } from "@chakra-ui/next-js"
import "mapbox-gl/dist/mapbox-gl.css"
import mapboxGl from "mapbox-gl"
import { Asset } from "@/app/types/assets"
import { MdOutlinePlace } from "react-icons/md"

type Props = {
  assets: Asset[]
  selectedAssets: string[]
  onChangeSelectedAssets: (selectedAssets: string[]) => void
}
const EnergyAssetsMap = ({
  assets,
  selectedAssets,
  onChangeSelectedAssets,
}: Props) => {
  const [selectedMarkers, setSelectedMarkers] =
    React.useState<string[]>(selectedAssets)

  return (
    <Box borderWidth={1} borderRadius="lg" overflow="hidden" h="800">
      <Map
        mapLib={mapboxGl}
        style={{ width: "100%", height: "100%" }}
        mapStyle="mapbox://styles/mapbox/streets-v9"
        mapboxAccessToken={configuration.mapboxAccessToken}
        initialViewState={{
          longitude: 19.9804,
          latitude: 46.4695,
          zoom: 15,
        }}
      >
        {assets.map(({ id, longitude, latitude, type, name, address }) => (
          <Marker
            key={id}
            longitude={longitude}
            latitude={latitude}
            anchor="bottom"
            onClick={() =>
              setSelectedMarkers((selection) => {
                if (selection.includes(id)) {
                  return selection.filter((selectedId) => selectedId !== id)
                }
                return [...selection, id]
              })
            }
            style={{ zIndex: selectedMarkers.includes(id) ? 1 : 0 }}
          >
            <Box
              zIndex={selectedMarkers.includes(id) ? 1 : 0}
              bgImage={`url('/assets/tooltip${selectedMarkers.includes(id) ? "-gray" : ""}.svg')`}
              w="64px"
              h="78px"
              cursor="pointer"
            >
              <Flex w="64px" h="64px" justify="center" align="center">
                <Image
                  src={`/assets/assetIcons/${type}.svg`}
                  alt={name}
                  width={ASSET_ICON_SIZE}
                  height={ASSET_ICON_SIZE}
                  maxWidth={ASSET_ICON_SIZE}
                />
              </Flex>
              {selectedMarkers.includes(id) && (
                <Flex
                  direction="column"
                  w={52}
                  bg="white"
                  borderRadius="lg"
                  mt={6}
                  borderWidth={1}
                  borderColor="green.600"
                  borderBottomLeftRadius={0}
                  boxShadow="md"
                  py={6}
                  px={4}
                  gap={2}
                >
                  <Image
                    src={`/assets/assetIcons/${type}.svg`}
                    alt={name}
                    width={ASSET_ICON_SIZE}
                    height={ASSET_ICON_SIZE}
                    maxWidth={ASSET_ICON_SIZE}
                    mb={2}
                  />
                  <Text fontSize="sm" fontWeight={500}>
                    {name}
                  </Text>
                  <Text
                    fontSize="sm"
                    display="flex"
                    alignItems="top"
                    gap={1}
                    color="gray.500"
                  >
                    <MdOutlinePlace
                      size={20}
                      style={{ flexGrow: 0, flexShrink: 0 }}
                    />{" "}
                    {address}
                  </Text>
                </Flex>
              )}
            </Box>
          </Marker>
        ))}
      </Map>
    </Box>
  )
}

export default EnergyAssetsMap
