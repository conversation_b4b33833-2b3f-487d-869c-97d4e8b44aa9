This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, install dependencies and run the development server:

```bash
yarn
yarn dev
```

Next, you should create a .env.local file based on the .env.example

Open [http://localhost:3003](http://localhost:3003) with your browser to see the result.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Installation and Setup

To use the test wallet and access the application, follow these steps:

1. **Download the Test Wallet:**
   - Go to the [Sporran Extension Releases](https://github.com/BTE-Trusted-Entity/sporran-extension/releases).
   - Download the test version from this link: [TEST-sporran-extension-2024.04.11.zip](https://github.com/BTE-Trusted-Entity/sporran-extension/releases/download/v2024.4.11/TEST-sporran-extension-2024.04.11.zip).

2. **Install the Extension:**
   - Unzip the downloaded file.
   - Open Google Chrome and navigate to `chrome://extensions/`.
   - Enable "Developer mode" using the toggle in the top right corner.
   - Click on "Load unpacked" and select the unzipped extension folder to install it.

3. **Create a Decentralized Identifier (DID):**
   - Once installed, open the Sporran extension in Chrome.
   - Follow the prompts to create an Identity
   - Click the Receive link and copy the Identity address on the next screen.
   - Visit the [Faucet](https://faucet.peregrine.kilt.io), paste the Identity address there, accept the Terms, and click Request Tokens to get some play KILT coins

4. **Register and Log In:**
   - Click on the "Join Now" button within the application interface.
   - Follow the registration process.
   - After completing registration, you can log in to the application.
