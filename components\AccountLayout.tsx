import { Box, Flex } from "@chakra-ui/react"
import { ReactNode } from "react"
import AccountMenu from "@/components/AccountMenu"

type Props = Readonly<{ children: ReactNode }>

const AccountLayout = ({ children }: Props) => {
  return (
    <Flex w="full" maxW={1200} mx="auto" flex={1}>
      <Flex w={280}>
        <AccountMenu />
      </Flex>
      <Box
        flex={1}
        borderLeftWidth={1}
        borderRightWidth={1}
        p={6}
        pos="relative"
        overflow="hidden"
      >
        {children}
      </Box>
    </Flex>
  )
}

export default AccountLayout
