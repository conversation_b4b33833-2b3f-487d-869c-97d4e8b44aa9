"use client"

import { <PERSON>, Spinner } from "@chakra-ui/react"
import React, { useRef, useState } from "react"

const Iframe = ({ src }: { src: string }) => {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [iframeHeight, setIframeHeight] = useState<number>(0)
  const handleLoadIframe = () => {
    setIframeHeight(
      iframeRef.current?.contentWindow?.document.body.scrollHeight ?? 0,
    )
    setIsLoading(false)
  }

  return (
    <>
      {isLoading && <Spinner />}
      <Box
        as="iframe"
        ref={iframeRef}
        h={iframeHeight}
        src={src}
        onLoad={handleLoadIframe}
        borderRadius="lg"
        borderWidth={1}
      />
    </>
  )
}

export default Iframe
