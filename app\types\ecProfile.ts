import { Feature, Point } from "geojson"

export type ECProfileFormValues = {
  nameOfEC: string
  location: Feature<Point>
  logo: File
  myDomain: string
  language: string
  country: string
  tso: string
  energyCommunityType: string
  typesOfMembership: string
  theme: string
  numberOfMembers: string
  citizensIndividual: string
  SMEs: string
  municipalities: string
  associations: string
  other: string
  website: string
  status: string
  activities: string
  services: string
  challengesInEstablishingTheEnergyCommunity: string
  challengesInTheLastYear: string
  ownershipStructure: string
  shareOfFemaleMembers: string
  shareOfVulnerableMembers: string
  shareOfMembersUnder35: string
  howDoYouInvolveMembersInDecisionMaking: string
  boardMembers: string
  votingSystem: string
  participationInLastYearsGeneralAssembly: string
}

export type ECProfileColorTheme = {
  label: string
  value: string
  colors: string[]
}
