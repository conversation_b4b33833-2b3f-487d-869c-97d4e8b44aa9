"use client"

import { <PERSON>actN<PERSON>, useState } from "react"
import { Card, CardBody, CardHeader } from "@chakra-ui/react"
import { MdKeyboardArrowDown, MdKeyboardArrowUp } from "react-icons/md"
import { Flex } from "@chakra-ui/react"

type Props = Readonly<{ name: string; children: ReactNode }>

const Fieldset = ({ name, children }: Props) => {
  const [isOpen, setIsOpen] = useState(true)

  return (
    <Card>
      <CardHeader
        as={Flex}
        align="center"
        justify="space-between"
        w="full"
        fontSize="lg"
        fontWeight="semibold"
        borderBottomWidth={isOpen ? 1 : 0}
      >
        {name}
        {isOpen ? (
          <MdKeyboardArrowUp role="button" onClick={() => setIsOpen(false)} />
        ) : (
          <MdKeyboardArrowDown role="button" onClick={() => setIsOpen(true)} />
        )}
      </CardHeader>
      <CardBody
        height={isOpen ? "auto" : "0px"}
        py={isOpen ? "4" : "0"}
        overflow="hidden"
        flex="none"
      >
        {children}
      </CardBody>
    </Card>
  )
}

export default Fieldset
