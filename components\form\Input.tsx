"use client"

import {
  FormControl,
  FormLabel,
  Input as ChakraInput,
  InputGroup,
  InputProps,
  InputRightAddon,
} from "@chakra-ui/react"
import { forwardRef } from "react"

type Props = {
  label: string
  rightAddon?: string
} & InputProps

const Input = forwardRef(function Input(
  { label, rightAddon, ...rest }: Props,
  ref,
) {
  return (
    <FormControl>
      <FormLabel>{label}</FormLabel>
      <InputGroup>
        <ChakraInput
          {...rest}
          ref={ref}
          borderRightWidth={!!rightAddon ? 0 : 1}
        />
        {!!rightAddon && <InputRightAddon>{rightAddon}</InputRightAddon>}
      </InputGroup>
    </FormControl>
  )
})

export default Input
