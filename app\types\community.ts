import { UserImportJobStatusType } from "@aws-sdk/client-cognito-identity-provider"

export type User = {
  id: string
  username: string
  email: string
  confirmation_status: boolean
  status: boolean
}

export type UserPool = {
  poolId: string
  arn: string
  name: string
  userCount: number
  createdAt: Date
  updatedAt: Date
}

export type ImportJob = {
  id: string
  name: string
  importedUsers: number
  skippedUsers: number
  failedUsers: number
  createdAt: Date
  status?: UserImportJobStatusType
}

export type ImportJobStatusChange = "start" | "stop"

export type ImportJobStartAction = "create" | "createAndStart"
