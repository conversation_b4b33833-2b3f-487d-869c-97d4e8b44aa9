import { Box, Button, Flex, Input } from "@chakra-ui/react"
import {
  AddressAutofill,
  AddressMinimap,
  config,
} from "@mapbox/search-js-react"
import configuration from "@/configuration"
import React from "react"

config.accessToken = configuration.mapboxAccessToken

type Props = {
  feature?: any
  locationIsSelected: boolean
  showAddressInput: boolean
  onRetrieve: (res: any) => void
  onClickChooseLocation: () => void
}

const SearchMap = ({
  feature,
  locationIsSelected,
  showAddressInput,
  onRetrieve,
  onClickChooseLocation,
}: Props) => (
  <Box position="relative" w="full" h={220} borderWidth={1} borderRadius="md">
    <Box position="absolute" left={0} top={0} w="full" h="full">
      {/* @ts-ignore */}
      <AddressMinimap
        feature={feature}
        show
        theme={{
          variables: {
            colorPrimary: "#20b24d",
            colorBackground: "silver",
          },
          icons: locationIsSelected ? {} : { marker: "" },
        }}
      />
    </Box>
    <Flex
      position="absolute"
      left={0}
      top={0}
      w="full"
      h="full"
      align="center"
      justify="center"
    >
      {showAddressInput ? (
        <Box w="full" m={10}>
          {/* @ts-ignore */}
          <AddressAutofill
            accessToken={configuration.mapboxAccessToken}
            onRetrieve={onRetrieve}
          >
            <Input
              placeholder="Start typing your address"
              bgColor="white"
              borderRadius="sm"
            />
          </AddressAutofill>
        </Box>
      ) : (
        <Button
          colorScheme="white"
          bg="gray.700"
          onClick={onClickChooseLocation}
          {...(locationIsSelected
            ? { position: "absolute", right: 4, bottom: 4, size: "sm" }
            : {})}
        >
          {locationIsSelected ? "Change" : "Choose"} location
        </Button>
      )}
    </Flex>
  </Box>
)

export default SearchMap
