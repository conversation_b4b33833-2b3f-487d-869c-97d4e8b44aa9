export interface PubSubSession {
  /** Configure the callback the extension must use to send messages to the dApp. Overrides previous values. */
  listen: (callback: EncryptedMessageCallback) => Promise<void>

  /** send the encrypted message to the extension */
  send: EncryptedMessageCallback

  /** close the session and stop receiving further messages */
  close: () => Promise<void>

  /** URI of the key agreement key of the temporary DID the extension will use to encrypt the session messages */
  encryptionKeyUri: string

  /** bytes as hexadecimal */
  encryptedChallenge: string

  /** 24 bytes nonce as hexadecimal */
  nonce: string
}

interface EncryptedMessageCallback {
  (message: EncryptedMessage): Promise<void>
}

export interface EncryptedMessage {
  /** URI of the key agreement key of the receiver D<PERSON> used to encrypt the message */
  receiverKeyUri: string

  /** URI of the key agreement key of the sender D<PERSON> used to encrypt the message */
  senderKeyUri: string

  /** ciphertext as hexadecimal */
  ciphertext: string

  /** 24 bytes nonce as hexadecimal */
  nonce: string
}
