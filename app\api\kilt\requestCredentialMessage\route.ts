import * as Kilt from "@kiltprotocol/sdk-js"
import { CType, Did, DidResourceUri, ICType } from "@kiltprotocol/sdk-js"
import kiltappUserCtype from "@/data/kilt/kiltappUserCtype.json"
import { fakeChallenge } from "@/components/kilt/Login"
import { encryptCallback, initializeKiltA<PERSON> } from "@/helpers/kilt"
import configuration from "@/configuration"

export const dynamic = "force-dynamic" // defaults to auto
export async function GET(request: Request) {
  const { dAppDidUri } = configuration

  await initializeKiltApi()

  const { searchParams } = new URL(request.url)

  const encryptionKeyUri = searchParams.get(
    "encryptionKeyUri",
  ) as DidResourceUri
  const { did } = Did.parse(encryptionKeyUri)
  const message = Kilt.Message.fromBody(
    {
      content: {
        cTypes: [
          {
            cTypeHash: CType.idToHash(kiltappUserCtype.$id as ICType["$id"]),
            requiredProperties: ["email", "name"],
          },
        ],
        challenge: fakeChallenge,
      },
      type: "request-credential",
    },
    dAppDidUri,
    did,
  )

  const encryptedMessage = await Kilt.Message.encrypt(
    message,
    encryptCallback,
    encryptionKeyUri,
  )

  return Response.json({ encryptedMessage })
}
