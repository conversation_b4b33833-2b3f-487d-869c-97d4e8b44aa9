import { ECProfileColorTheme } from "@/app/types/ecProfile"

const generateCognitoCSS = (theme: ECProfileColorTheme) => {
  const primaryColor = theme.colors[0]

  return `.logo-customizable {
    max-width: 60%;
    max-height: 30%;
}

.banner-customizable {
    padding: 15px 0;
    background-color: white;
    border-bottom: 1px solid #EAECF0;
}

.label-customizable {
    margin: 6px 0 3px 0;
    font-weight: 400;
    font-size: 14px;
    color: #6F7278;
}

.textDescription-customizable {
    padding-top: 10px;
    padding-bottom: 14px;
    display: block;
    font-size: 15px;
    text-align: center;
    color: ${primaryColor};
}

.idpDescription-customizable {
    padding-top: 10px;
    padding-bottom: 10px;
    display: block;
    font-size: 16px;
}

.legalText-customizable {
    color: #747474;
    font-size: 11px;
}

.submitButton-customizable {
    font-size: 14px;
    font-weight: bold;
    margin: 20px 0px 10px 0px;
    height: 40px;
    width: 100%;
    color: #fff;
    background-color: ${primaryColor};
    border-radius: 20px;
    border: none;
    margin: 20px 0;
}

.submitButton-customizable:hover {
    color: #fff;
    background-color: ${primaryColor};
    filter: brightness(60%);
}

.errorMessage-customizable {
    padding: 5px;
    font-size: 14px;
    width: 100%;
    background: #F5F5F5;
    border: 2px solid #D64958;
    color: #D64958;
}

.inputField-customizable {
    width: 100%;
    height: 38px;
    color: #555;
    background-color: #fff;
    border: 1px solid #ccc;
    box-shadow: none;
}

.inputField-customizable:focus {
    border-color: #66afe9;
    outline: 0;
}

.idpButton-customizable {
    height: 40px;
    width: 100%;
    width: 100%;
    text-align: center;
    margin-bottom: 15px;
    color: #fff;
    background-color: #5bc0de;
    border-color: #46b8da;
}

.idpButton-customizable:hover {
    color: #fff;
    background-color: #31b0d5;
}

.socialButton-customizable {
    border-radius: 2px;
    height: 40px;
    margin-bottom: 15px;
    padding: 1px;
    text-align: left;
    width: 100%;
}

.redirect-customizable {
    color: #3B5DD5;
    text-decoration: none;
    text-align: center;
}

.passwordCheck-notValid-customizable {
    color: #DF3312;
}

.passwordCheck-valid-customizable {
    color: ${primaryColor};
}

.background-customizable {
    background: #ffffff;
    border-radius: 10px;
}
`
}

export default generateCognitoCSS
