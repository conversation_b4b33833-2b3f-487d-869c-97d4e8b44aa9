import React, { useState, ReactNode } from "react"
import ReactCopyToClipboard from "react-copy-to-clipboard"
import { MdDone } from "react-icons/md"
import { But<PERSON> } from "@chakra-ui/react"

type Props = {
  children: ReactNode | ReactNode[]
  text: string
  [x: string]: any
}

const CopyToClipboard = ({ children, text, ...props }: Props) => {
  const [copied, setCopied] = useState(false)

  const handleCopy = () => {
    setCopied(true)

    setTimeout(() => {
      setCopied(false)
    }, 1000)
  }

  const copy = children
  const done = <MdDone />

  return (
    <ReactCopyToClipboard text={text} onCopy={() => handleCopy()}>
      <Button variant="link" minW="auto" verticalAlign="middle" mr={2}>
        {copied ? done : copy}
      </Button>
    </ReactCopyToClipboard>
  )
}

export default CopyToClipboard
