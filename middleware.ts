import type { NextRequest } from "next/server"
import { isLoggedIn } from "@/helpers/authentication"
import createMiddleware from "next-intl/middleware"
import { extractLanguageCodeFromPathname } from "./helpers/language"
import { routing } from "./i18n/config"

const intlMiddleware = createMiddleware(routing)

export function middleware(request: NextRequest) {
  const response = intlMiddleware(request)
  const selectedLanguageCode = extractLanguageCodeFromPathname(
    request.nextUrl.pathname,
  )

  if (
    !isLoggedIn() &&
    request.nextUrl.pathname.startsWith(`/${selectedLanguageCode}/account`)
  ) {
    return Response.redirect(new URL("/", request.url))
  }

  if (response) return response // If intl middleware returns a response, use it
}

export const config = {
  // Match only internationalized pathnames
  matcher: [
    "/",
    "/(hu|en)/:path*",
    "/((?!api|.well-known|_next/static|assets|scripts|_next/image|favicon.ico).*)",
  ],
}
