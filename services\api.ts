import { ECProfileFormValues } from "@/app/types/ecProfile"
import { EncryptedMessage } from "@/app/types/kilt"
import {
  ImportJobStartAction,
  ImportJobStatusChange,
} from "@/app/types/community"

export const API_ROUTES = {
  community: {
    users: "/api/community/users",
    importJobs: "/api/community/importJobs",
    deleteUser: "/api/community/users",
    ui: {
      _default: "/api/community/ui",
      signUp: {
        preview: "/api/community/ui/signUp/preview",
      },
      signIn: {
        preview: "/api/community/ui/signIn/preview",
      },
    },
  },
  account: {
    ecProfile: "/api/account/ec-profile",
  },
  kilt: {
    attestCredential: "/api/kilt/attestCredential",
    isLoggedIn: "/api/kilt/isLoggedIn",
    logOut: "/api/kilt/logOut",
    requestAttestationMessage: "/api/kilt/requestAttestationMessage",
    requestCredentialMessage: "/api/kilt/requestCredentialMessage",
    verifyCredential: "/api/kilt/verifyCredential",
  },
}

const request = async (
  url: string,
  params: Record<string, string> | FormData = {},
  method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE" = "GET",
) => {
  let options: {
    method: "GET" | "POST" | "PUT" | "PATCH" | "DELETE"
    body?: string | FormData
  } = {
    method,
  }
  if (params instanceof FormData) {
    options.body = params
  } else if (Object.keys(params).length > 0) {
    if ("GET" === method) {
      url += "?" + new URLSearchParams(params).toString()
    } else {
      options.body = JSON.stringify(params)
    }
  }

  const response = await fetch(url, options)

  if (response.status === 204) {
    return
  }

  if (response.status >= 500) {
    throw new Error("API error", { cause: await response.json() })
  }

  return await response.json()
}

const get = (url: string, params?: Record<string, string>) =>
  request(url, params, "GET")
const post = (url: string, params?: Record<string, any>) =>
  request(url, params, "POST")
const put = (url: string, params?: Record<string, any>) =>
  request(url, params, "PUT")
const patch = (url: string, params?: Record<string, any>) =>
  request(url, params, "PATCH")
const del = (url: string, params?: Record<string, any>) =>
  request(url, params, "DELETE")

export const getPoolUsers = async ({
  filterProperty,
  filterValue,
}: {
  filterProperty: string
  filterValue: string
}) => {
  const params = {
    ...(filterValue ? { filter: `"${filterProperty}"^="${filterValue}"` } : {}),
  }

  const data = await get(API_ROUTES.community.users, params)

  return data ?? []
}

export const postPoolUser = async (username: string, email: string) =>
  post(API_ROUTES.community.users, { username, email })

export const deletePoolUser = async (username: string) =>
  del(`${API_ROUTES.community.deleteUser}/${username}`)

export const getImportJobs = async () => {
  const data = await get(API_ROUTES.community.importJobs)

  return data ?? []
}

export const postImportJob = async (
  usersFile: File,
  action: ImportJobStartAction,
) => {
  const formData = new FormData()
  formData.append("usersFile", usersFile)
  formData.append("action", action)

  return post(API_ROUTES.community.importJobs, formData)
}

export const patchImportJob = async (
  jobId: string,
  changeStatus: ImportJobStatusChange,
) => {
  return patch(`${API_ROUTES.community.importJobs}/${jobId}`, { changeStatus })
}

export const putCommunityUI = async (logo: File, css: string) => {
  const formData = new FormData()
  formData.append("logo", logo)
  formData.append("css", css)

  return put(`${API_ROUTES.community.ui._default}`, formData)
}

export const postEcProfile = async (values: ECProfileFormValues) =>
  post(API_ROUTES.account.ecProfile, values)

export const postAttestCredential = async (message: EncryptedMessage) =>
  post(API_ROUTES.kilt.attestCredential, { message })

export const getIsLoggedIn = async () => get(API_ROUTES.kilt.isLoggedIn)

export const postLogOut = async () => post(API_ROUTES.kilt.logOut)

export const getRequestAttestationMessage = async ({
  firstName,
  lastName,
  email,
  encryptionKeyUri,
}: {
  firstName: string
  lastName: string
  email: string
  encryptionKeyUri: string
}) =>
  get(API_ROUTES.kilt.requestAttestationMessage, {
    firstName,
    lastName,
    email,
    encryptionKeyUri,
  })

export const getRequestCredentialMessage = async ({
  encryptionKeyUri,
}: {
  encryptionKeyUri: string
}) => get(API_ROUTES.kilt.requestCredentialMessage, { encryptionKeyUri })

export const postVerifyCredential = async ({
  message,
}: {
  message: EncryptedMessage
}) => post(API_ROUTES.kilt.verifyCredential, { message })
