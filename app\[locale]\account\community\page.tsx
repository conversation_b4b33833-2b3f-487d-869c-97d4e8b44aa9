import PageHeader from "@/components/PageHeader"
import React from "react"
import UserPoolOverview from "@/components/community/UserPoolOverview"
import Tabs from "@/components/community/Tabs"
import { Stack } from "@chakra-ui/react"
import { getPool } from "@/services/cognito/cognito"

const Page = async () => {
  const pool = await getPool("us-east-1_sIDIquFWP")

  if (!pool) {
    return null
  }

  const { poolId, arn, name, userCount, createdAt, updatedAt } = pool

  return (
    <>
      <PageHeader title="Community" />
      <Stack spacing={4}>
        <UserPoolOverview
          poolId={poolId}
          arn={arn}
          name={name}
          userCount={userCount}
          createdAt={createdAt}
          updatedAt={updatedAt}
        />
        <Tabs userCount={userCount} />
      </Stack>
    </>
  )
}

export default Page
