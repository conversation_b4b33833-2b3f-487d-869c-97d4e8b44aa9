"use client"

import {
  FormControl,
  FormLabel,
  Select as ChakraSelect,
  InputGroup,
  SelectProps,
} from "@chakra-ui/react"
import { forwardRef } from "react"
import { MdArrowDropDown } from "react-icons/md"

type StringOptions = string[]
type ObjectOptions = { label: string; value: string }[]
type Options = ObjectOptions | StringOptions

type Props = {
  label: string
  options: Options
  rightAddon?: string
} & SelectProps

const Input = forwardRef(function Input(
  { label, rightAddon, options, ...rest }: Props,
  ref,
) {
  return (
    <FormControl>
      <FormLabel>{label}</FormLabel>
      <InputGroup>
        <ChakraSelect icon={<MdArrowDropDown />} {...rest} ref={ref}>
          {optionElements(options)}
        </ChakraSelect>
      </InputGroup>
    </FormControl>
  )
})

const optionElements = (options: Options) => {
  if (typeof options[0] === "string") {
    return (options as StringOptions).map((option) => (
      <option key={option} value={option}>
        {option}
      </option>
    ))
  }

  return (options as ObjectOptions).map((option) => (
    <option key={option.value} value={option.value}>
      {option.label}
    </option>
  ))
}

export default Input
