"use client"

import { Box, Flex, Text } from "@chakra-ui/react"
import React from "react"
import { Link, Image } from "@chakra-ui/next-js"
import Login from "@/components/kilt/Login"
import LanguageSwitcher from "./LanguageSwitcher"

type Props = Readonly<{ children: React.ReactNode }>

const Layout = ({ children }: Props) => (
  <Flex w="full" direction="column" minH="100dvh">
    <Flex
      as="header"
      w="full"
      direction="column"
      align="center"
      borderBottomWidth={1}
    >
      <Flex justify="space-between" w="full" maxW={1200} p={4}>
        <Link href="/">
          <Image
            src="/assets/logo.svg"
            alt="Decent.ec logo"
            title="Decent.ec"
            width={240}
            height={10}
            p={2}
          />
        </Link>
        <Flex gap={4}>
          <LanguageSwitcher />
          <Login />
        </Flex>
      </Flex>
    </Flex>
    <Flex as="main" direction="column" w="full" flex={1}>
      {children}
    </Flex>
    <Flex w="full" as="footer" justify="center" mt="auto" borderTopWidth={1}>
      <Flex
        justify="space-between"
        align="center"
        w="full"
        maxW={1200}
        p={4}
        flexWrap="wrap"
        gap={4}
      >
        <Flex flexWrap="wrap" gap={4}>
          <Text maxW={400}>
            DECENT.EC, Decentralized Energy Communities™
            <br />
            is an initiative of MET3R Solutions Limited.
          </Text>
          <Link href="mailto:<EMAIL>" whiteSpace="nowrap">
            <Image
              src="/assets/icons/mail.svg"
              alt="Email icon"
              width={5}
              height={4}
              display="inline"
              verticalAlign="baseline"
              mr={1}
              position="relative"
              top={0.5}
            />
            <EMAIL>
          </Link>
        </Flex>
        <Text whiteSpace="nowrap">
          Design by{" "}
          <Image
            src="/assets/owento.svg"
            alt="Owento logo"
            width={16}
            height={3.5}
            display="inline"
            position="relative"
            top={0.5}
          />
        </Text>
      </Flex>
    </Flex>
  </Flex>
)

export default Layout
