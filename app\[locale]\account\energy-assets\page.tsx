"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON><PERSON>utton,
  <PERSON><PERSON><PERSON>ontent,
  ModalHeader,
  ModalOverlay,
  Stat,
  StatLabel,
  StatNumber,
} from "@chakra-ui/react"
import React from "react"
import PageHeader from "@/components/PageHeader"
import EnergyAssetsViewSwitch from "@/components/energyAssets/EnergyAssetsViewSwitch"
import EnergyAssetsTable from "@/components/energyAssets/EnergyAssetsTable"
import EnergyAssetsMap from "@/components/energyAssets/EnergyAssetsMap"
import { Asset } from "@/app/types/assets"
import AddEnergyAssetPopover from "@/components/energyAssets/AddEnergyAssetPopover"
import energyAssetTypes from "@/data/energyAssetTypes"
import AssetActions from "./components/AssetActions"

const Page = () => {
  const [view, setView] = React.useState<"list" | "map">("list")
  const [selectedAssets, setSelectedAssets] = React.useState<string[]>([])
  const [addAssetTypeId, setAddAssetTypeId] = React.useState<string | null>(
    null,
  )

  const assets: Asset[] = [
    {
      id: "1",
      name: "Asset 1",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Active",
      latitude: 46.4695,
      longitude: 19.9833,
      type: "consumer",
      address: "Petőfi Sándor utca 12, Kistelek",
    },
    {
      id: "2",
      name: "Asset 2",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Inactive",
      latitude: 46.4695,
      longitude: 19.9804,
      type: "consumer",
      address: "Kossuth utca 6, Kistelek",
    },
    {
      id: "3",
      name: "Asset 3",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Active",
      latitude: 46.4675,
      longitude: 19.9844,
      type: "consumer",
      address: "Dózsa György tér 3, Kistelek",
    },
    {
      id: "4",
      name: "Asset 4",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Active",
      latitude: 46.4715,
      longitude: 19.9833,
      type: "consumer",
      address: "Szabadság út 17, Kistelek",
    },
    {
      id: "5",
      name: "Asset 5",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Inactive",
      latitude: 46.4715,
      longitude: 19.98,
      type: "consumer",
      address: "Arany János utca 9, Kistelek",
    },
    {
      id: "6",
      name: "Asset 6",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Active",
      latitude: 46.4745,
      longitude: 19.9833,
      type: "consumer",
      address: "Fő út 25, Kistelek",
    },
    {
      id: "7",
      name: "Asset 7",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Active",
      latitude: 46.4745,
      longitude: 19.9763,
      type: "consumer",
      address: "Bajcsy-Zsilinszky út 8, Kistelek",
    },
    {
      id: "8",
      name: "Asset 8",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Inactive",
      latitude: 46.4665,
      longitude: 19.9763,
      type: "consumer",
      address: "Jókai utca 14, Kistelek",
    },
    {
      id: "9",
      name: "Asset 9",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Active",
      latitude: 46.4665,
      longitude: 19.9793,
      type: "consumer",
      address: "Rákóczi út 31, Kistelek",
    },
    {
      id: "10",
      name: "Asset 10",
      consumptionCapacity: "100 kW",
      consumptionProfile: "Profile",
      productionCapacity: "200 kW",
      productionProfile: "Profile",
      status: "Inactive",
      latitude: 46.4685,
      longitude: 19.9753,
      type: "consumer",
      address: "Széchenyi utca 5, Kistelek",
    },
  ]

  const handleChangeSelectedAssets = (selectedAssets: string[]) =>
    setSelectedAssets(selectedAssets)

  const addAssetType = energyAssetTypes.find(({ id }) => id === addAssetTypeId)

  return (
    <>
      <PageHeader
        title="Energy assets"
        description="Add or edit your energy assets"
        actions={
          <Flex justify="end" gap={4}>
            <EnergyAssetsViewSwitch view={view} onSetView={setView} />
            <AddEnergyAssetPopover
              onSelect={(typeId) => setAddAssetTypeId(typeId)}
            />
          </Flex>
        }
      />
      <Flex direction="column" gap={6}>
        <HStack spacing={4}>
          <Stat>
            <StatLabel>Total assets</StatLabel>
            <StatNumber>32</StatNumber>
          </Stat>
          <Stat>
            <StatLabel>Total production</StatLabel>
            <StatNumber>350 kW</StatNumber>
          </Stat>
          <Stat>
            <StatLabel>Total consumption</StatLabel>
            <StatNumber>Value</StatNumber>
          </Stat>
        </HStack>
        {view === "list" ? (
          <EnergyAssetsTable
            assets={assets}
            selectedAssets={selectedAssets}
            onChangeSelectedAssets={handleChangeSelectedAssets}
          />
        ) : (
          <EnergyAssetsMap
            assets={assets}
            selectedAssets={selectedAssets}
            onChangeSelectedAssets={handleChangeSelectedAssets}
          />
        )}
      </Flex>
      {!!selectedAssets.length && <AssetActions />}
      <Modal
        isOpen={!!addAssetTypeId}
        onClose={() => setAddAssetTypeId(null)}
        isCentered
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Add {addAssetType?.name}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>{addAssetType?.name} form</ModalBody>
        </ModalContent>
      </Modal>
    </>
  )
}

export default Page
