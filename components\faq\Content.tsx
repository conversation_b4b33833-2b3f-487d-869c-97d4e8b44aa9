import {
  Accordion,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>I<PERSON>,
  OrderedList,
  Text,
} from "@chakra-ui/react"
import AccordionItem from "@/components/faq/AccordionItem"
import { Link } from "@/i18n/navigation"
import ArticleImage from "@/components/articles/ArticleImage"
import React from "react"
import configuration from "@/configuration"
import { useTranslations } from "next-intl"

type Props = Readonly<{
  locale: string
  onClickDescriptionLink: (key: string) => void
}>

const Content = ({ locale, onClickDescriptionLink }: Props) => {
  const { s3ImagesURL } = configuration
  const t = useTranslations("HomePage")

  if (locale === "en") {
    return (
      <Box
        m="auto"
        w="full"
        maxW={800}
        pt={16}
        textAlign="center"
        sx={{ "*": { fontFamily: "Lexend Deca" } }}
      >
        <Heading as="h2">Frequently asked questions</Heading>
        <Heading as="h3" size="md" mt={6} fontWeight={400} color="#A1A5AB">
          Everything you need to know about us
        </Heading>
        <Accordion textAlign="left" my={16} allowToggle>
          <AccordionItem title="What is Decent and how do I get started?">
            <Text color="gray.400">
              Decent stands for Decentralized, which refers to the types of
              Energy Communities we work with. We provide AI models, community
              tools, knowledge base articles, hardware, and software to manage
              and operate the community (asset registration apps,{" "}
              <Button
                variant="link"
                onClick={() => onClickDescriptionLink("virtualSCADA")}
              >
                virtual SCADA
              </Button>
              , Billing Portal). To get started you have to create a
              decentralized ID to sign up, and we will create your{" "}
              <Button
                variant="link"
                onClick={() => onClickDescriptionLink("energyCommunityPortal")}
              >
                energy community domain
              </Button>
              , then after configuring your preferences, you can launch your
              <Button
                variant="link"
                onClick={() => onClickDescriptionLink("signUpPage")}
              >
                sign-up page
              </Button>
              . To register <Link href="/registration">fill out the form</Link>.
            </Text>
          </AccordionItem>
          <AccordionItem title="What are Energy Communities?">
            <Text color="gray.400">
              An Energy Community is a self-organized group of individuals,
              social entities, or associations aiming to achieve a 100%
              renewable energy model. These communities generate energy from
              renewable sources like solar, wind, hydro, or biogas and
              prioritize social and environmental benefits. The members can save
              on their energy bills and collectively own the energy
              infrastructure and they play a crucial role in contributing to a
              greener planet.{" "}
              <Link href="/faq/what-are-energy-communities">
                {t("readMore")}
              </Link>
            </Text>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-01.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
          <AccordionItem title="How to start an Energy Community?">
            <Text color="gray.400">
              To start an Energy Community you have to find a pre-existing group
              or create your own by raising awareness about the energy
              transition. Research is an essential step including understanding
              the objectives, the forms, the legal regulations, the possible
              challenges, and the funding options. After your research, these
              are the main steps of starting an Energy Community:
            </Text>
            <OrderedList>
              <ListItem>Clarifying the roles, objectives, and members</ListItem>
              <ListItem>Choosing the Legal Model</ListItem>
              <ListItem>Defining the Shared Energy Services</ListItem>
              <ListItem>
                Planning the social, energy, and economic impact
              </ListItem>
              <ListItem>Choosing the Financing Model</ListItem>
            </OrderedList>
            <Link href="/faq/how-to-start-an-energy-community">
              {t("readMore")}
            </Link>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-02.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
          <AccordionItem title="How to achieve 100% Renewable Energy Generation?">
            <Text color="gray.400">
              Energy Communities can use various sources such as solar panels,
              wind, biogas, or biomass to achieve this objective. Improving
              energy efficiency is a key element, including reducing the impact
              of the current mobility model by reducing the necessity for travel
              and using public transportation. Another key element is
              discovering different heating and cooling solutions and choosing
              more sustainable options in our everyday lives. The aim is to use
              less energy for the same activities by implementing renewable
              energy usage.{" "}
              <Link href="/faq/how-to-achieve-100-percent-renewable-energy-generation">
                {t("readMore")}
              </Link>
            </Text>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-04.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
          <AccordionItem title="How can you contribute with self-consumption and collectively?">
            <Text color="gray.400">
              To help achieve 100% renewable energy generation in Energy
              Communities, members should focus on both self-consumption and
              collective efforts. Members can contribute by collective
              self-production, sharing the generated energy and consuming energy
              generated by the community, instead of buying electricity from the
              grid. This method reduces reliance on the grid and lowers costs.{" "}
              <Link href="/faq/how-can-you-contribute-with-self-consumption-and-collectively">
                {t("readMore")}
              </Link>
            </Text>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-05.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
          <AccordionItem title="How to implement Shared Electric Mobility?">
            <Text color="gray.400">
              To implement shared electric mobility in an Energy Community, it’s
              crucial to understand your local environment and adapt services to
              minimize the impact by implementing &quot;15-minute city&quot; (a
              model prioritizing pedestrians and cyclists) principles and
              enhancing infrastructure by increasing bus frequencies,
              diversifying routes, and developing cycle roads. Throughout the
              process, you have to provide dedicated parking and charging spots
              and build a governance structure for decision-making within the
              community. Optimizing vehicle usage based on community needs is an
              essential step that helps reduce the environmental impact and
              improve mobility options.{" "}
              <Link href="/faq/how-to-implement-shared-electric-mobility">
                {t("readMore")}
              </Link>
            </Text>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-03.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
          <AccordionItem title="How do electric charging points work in Energy Communities?">
            <Text color="gray.400">
              Electric charging points in Energy Communities reduce energy
              consumption and support the shift to renewable energy. They
              recharge EV batteries by communicating with the vehicle to
              optimize charging power and battery health. Various connectors
              (Type 1, Type 2, Combo, CHADeMO) and charging modes (Mode 1 to
              Mode 3) are used for different needs. Charging time depends on
              battery status, temperature, and system performance.{" "}
              <Link href="/faq/how-do-electric-charging-points-work-in-energy-communities">
                {t("readMore")}
              </Link>
            </Text>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-07.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
          <AccordionItem title="Which legal form to choose for an Energy Community?">
            <Text color="gray.400">
              Choosing the right legal form depends on your community&apos;s
              goals and needs. A cooperative is ideal for member-owned,
              profit-sharing, and democratic decision-making, but it has limited
              capital access. A non-profit focuses on social and environmental
              impact, offering tax benefits, but does not distribute profits. A
              limited liability company (LLC) provides operational flexibility
              and profit generation, protecting members&apos; personal assets,
              but it is complex to establish. Consider your financial goals,
              mission, funding needs, and local requirements to determine the
              best fit.{" "}
              <Link href="/faq/which-legal-form-to-choose-for-an-energy-community">
                {t("readMore")}
              </Link>
            </Text>
            <ArticleImage
              src={`${s3ImagesURL}/faq/faq-06.svg`}
              width={780}
              height={408}
              alt=""
            />
          </AccordionItem>
        </Accordion>
      </Box>
    )
  }

  return (
    <Box
      m="auto"
      w="full"
      maxW={800}
      pt={16}
      textAlign="center"
      sx={{ "*": { fontFamily: "Lexend Deca" } }}
    >
      <Heading as="h2">Frequently asked questions</Heading>
      <Heading as="h3" size="md" mt={6} fontWeight={400} color="#A1A5AB">
        Everything you need to know about us
      </Heading>
      <Accordion textAlign="left" my={16} allowToggle>
        <AccordionItem title="Mit jelent a Decent, hogyan kezdjek neki?">
          <Text color="gray.400">
            A Decent a decentralizált kifejezés rövidítése, amely az
            energiaközösségekre utal, akikkel együtt dolgozunk. MI modelleket,
            közösségi eszközöket, edukációs-cikkeket, hardvert, valamint
            szoftvert biztosítunk a közösség kezeléséhez és működtetéséhez
            (eszközregisztrációs alkalmazások,{" "}
            <Button
              variant="link"
              onClick={() => onClickDescriptionLink("virtualSCADA")}
            >
              virtuális SCADA
            </Button>
            , Számlázási Portál). A regisztrációhoz hozz létre egy
            decentralizált azonosítót és mi létrehozzuk az{" "}
            <Button
              variant="link"
              onClick={() => onClickDescriptionLink("energyCommunityPortal")}
            >
              energiaközösséged domainjét
            </Button>
            . Ezt követően beállíthatod a preferenciáidat, majd elindíthatod a{" "}
            <Button
              variant="link"
              onClick={() => onClickDescriptionLink("signUpPage")}
            >
              regisztrációs oldaladat
            </Button>
            . A regisztrációhoz{" "}
            <Link href="/registration">töltsd ki az űrlapot</Link>.
          </Text>
        </AccordionItem>
        <AccordionItem title="Mik azok az energiaközösségek?">
          <Text color="gray.400">
            Az energiaközösség egy önszerveződő csoport, amely egyéneket,
            társadalmi szervezeteket vagy egyesületeket foglal magába és a
            céljuk egy 100%-ban megújuló energia modell megvalósítása. Ezek a
            közösségek megújuló forrásokból állítják elő az energiát, mint
            például a napenergia, a szélenergia, a vízenergia vagy a biogáz és
            előtérbe helyezik a társadalmi és környezeti előnyöket. A tagok
            spórolhatnak energiaszámláikon, közösen birtokolhatják az
            energia-infrastruktúrát és kulcsfontosságú szerepet játszanak egy
            zöldebb bolygó megteremtéséhez.{" "}
            <Link href="/faq/what-are-energy-communities">{t("readMore")}</Link>
          </Text>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-01.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
        <AccordionItem title="Hogyan alapíts energiaközösséget?">
          <Text color="gray.400">
            Az energiaközösség létrehozásához vagy egy már meglévő csoporthoz
            kell csatlakoznod, vagy létre kell hoznod a saját közösségedet, amit
            az energia átmenet fontosságának felhívásával tehetsz meg. A
            kutatómunka elengedhetetlen lépés, amely magában foglalja a közösség
            céljai, a formái, a jogi szabályozások a lehetséges kihívások és a
            finanszírozási lehetőségek megértését. A kutatást követően ezek az
            energiaközösség létrehozásának fő lépései:
          </Text>
          <OrderedList>
            <ListItem>A szerepek, célok és tagok tisztázása</ListItem>
            <ListItem>A jogi forma kiválasztása</ListItem>
            <ListItem>A közös energiaszolgáltatások meghatározása</ListItem>
            <ListItem>
              A szociális, energia- és gazdasági hatás megtervezése
            </ListItem>
            <ListItem>A finanszírozási modell kiválasztása</ListItem>
          </OrderedList>
          <Link href="/faq/how-to-start-an-energy-community">
            {t("readMore")}
          </Link>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-02.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
        <AccordionItem title="Hogyan lehet elérni a 100%-ban megújuló energia előállítását?">
          <Text color="gray.400">
            Az energiaközösségek különböző forrásokat használhatnak, például
            napelemeket, szélerőt, biogázt vagy biomasszát, a cél elérésének
            érdekében. Az energiahatékonyság javítása kulcsfontosságú elem,
            beleértve az aktuális mobilitási modell környezeti hatásának
            csökkentését, az utazási szükségletek csökkentésével és a
            tömegközlekedés használatával. Egy másik kulcsfontosságú elem a
            különböző fűtési és hűtési megoldások felfedezése és fenntarthatóbb
            megoldásokra való váltás a mindennapok során. A cél, hogy
            ugyanazokhoz a tevékenységekhez kevesebb energiát használjunk fel a
            megújuló energia alkalmazásával.{" "}
            <Link href="/faq/how-to-achieve-100-percent-renewable-energy-generation">
              {t("readMore")}
            </Link>
          </Text>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-04.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
        <AccordionItem title="Hogyan járulhatsz hozzá saját és kollektív fogyasztással?">
          <Text color="gray.400">
            Az energiaközösségek 100%-ban megújuló energia előállításához a
            tagoknak egyaránt összpontosítaniuk kell a saját és a kollektív
            fogyasztásra. A tagok hozzájárulhatnak kollektív és öntermeléssel,
            az előállított energia megosztásával és a közösség által generált
            energia fogyasztásával ahelyett, hogy a hálózatról vásárolnának
            áramot. Ez a módszer egyaránt csökkenti a hálózattól való függőséget
            és a költségeket.{" "}
            <Link href="/faq/how-can-you-contribute-with-self-consumption-and-collectively">
              {t("readMore")}
            </Link>
          </Text>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-05.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
        <AccordionItem title="Hogyan lehet megvalósítani a megosztott e-mobilitást?">
          <Text color="gray.400">
            A megosztott elektromos mobilitás megvalósításához egy
            energiaközösségben fontos megérteni a helyi adottságokat, és az
            igényekhez igazítani a szolgáltatásokat, hogy minimalizáljuk a
            környezeti hatást a &quot;15 perces városok&quot; (a gyalogosokat és
            kerékpárosokat előtérbe helyező modell) elvei alapján, valamint
            fejleszteni az infrastruktúrát, a buszjáratok gyakoriságának
            növelésével, az útvonalak diverzifikálásával, és a kerékpárutak
            fejlesztésével. A folyamat során biztosítani kell a kijelölt
            parkolóhelyeket és töltőpontokat, valamint létrehozni egy irányítási
            struktúrát a közösségen belüli döntéshozatalhoz. Az igényeknek
            megfelelő járműhasználat optimalizálása fontos lépés, amely segít
            csökkenteni a környezeti hatásokat és javítja a mobilitási
            lehetőségeket.{" "}
            <Link href="/faq/how-to-implement-shared-electric-mobility">
              {t("readMore")}
            </Link>
          </Text>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-03.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
        <AccordionItem title="Hogyan működnek az elektromos töltőpontok az energiaközösségekben?">
          <Text color="gray.400">
            Az elektromos töltőpontok az Energia Közösségekben csökkentik az
            energiafogyasztást és támogatják az átállást a megújuló energiára.
            Az EV akkumulátorokat úgy töltik, hogy kommunikálnak a járművel a
            töltési teljesítmény és az akkumulátor egészségének optimalizálása
            érdekében. Különböző csatlakozók (Type 1, Type 2, Combo, CHADeMO) és
            töltési módok (Mode 1-től Mode 3-ig) állnak rendelkezésre a
            különböző igényekhez. A töltési idő függ az akkumulátor állapotától,
            a hőmérséklettől és a rendszer teljesítményétől.{" "}
            <Link href="/faq/how-do-electric-charging-points-work-in-energy-communities">
              {t("readMore")}
            </Link>
          </Text>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-07.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
        <AccordionItem title="Milyen jogi formát válasszunk egy energiaközösség megalapításához?">
          <Text color="gray.400">
            A megfelelő jogi forma kiválasztása elsősorban a közösség céljaitól
            és igényeitől függ. A szövetkezet ideális tagtulajdonú,
            profitmegosztó és demokratikus döntéshozatalú közösségek számára, de
            korlátozott tőkebevonási lehetőségekkel rendelkezik. A non-profit
            szervezetek a szociális és környezeti hatásokra összpontosítanak,
            lehetősé van adókedvezményekre, de nem osztható szét a nyereség a
            tagok között. A korlátolt felelősségű társaság (Kft.) rugalmasságot
            és profittermelést biztosít, védve a tagok személyes vagyonát, de a
            megalapítása komplex. A jogi forma kiválasztásakor vedd figyelembe a
            közösség pénzügyi céljait, küldetését, finanszírozási szükségleteit
            és helyi követelményeket az ideális forma megtalálásához.{" "}
            <Link href="/faq/which-legal-form-to-choose-for-an-energy-community">
              {t("readMore")}
            </Link>
          </Text>
          <ArticleImage
            src={`${s3ImagesURL}/faq/faq-06.svg`}
            width={780}
            height={408}
            alt=""
          />
        </AccordionItem>
      </Accordion>
    </Box>
  )
}

export default Content
