"use client"

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "@chakra-ui/react"
import React, { useEffect, useState } from "react"
import { logOut, requestKiltCredentialFromSporran } from "@/helpers/kilt"
import { useRouter } from "next/navigation"
import { Image } from "@chakra-ui/next-js"
import useKiltSessionStore from "@/hooks/useKiltSessionStore"
import useAuthenticationStore from "@/hooks/useAuthenticationStore"
import { getIsLoggedIn } from "@/services/api"
import MissingSporranModal from "@/components/missingSporran/MissingSporranModal"
import { DEFAULT_AUTH_ROUTE } from "@/constants"

//todo: implement
export const fakeChallenge =
  "0xf731172962047733b41a27ba247f3781ebfb26daacffebb2"

const Login = () => {
  const router = useRouter()
  const session = useKiltSessionStore(({ session }) => session)
  const { loggedIn, setLoggedIn } = useAuthenticationStore()
  const [isLoading, setIsLoading] = useState(true)
  const [missingSporranModalIsOpen, setMissingSporranModalIsOpen] = useState(false)

  useEffect(() => {
    const checkLoggedIn = async () => {
      const { loggedIn } = await getIsLoggedIn()
      setLoggedIn(loggedIn)
      setIsLoading(false)
    }
    void checkLoggedIn()
  }, [setLoggedIn])

  const handleClickLoginWithKilt = async () => {
    setIsLoading(true)
    try {
      await requestKiltCredentialFromSporran(session)
      router.push(DEFAULT_AUTH_ROUTE)
      setLoggedIn(true)
    } catch (error) {
      setMissingSporranModalIsOpen(true)
      console.error("Error requesting KILT credential from Sporran:", error)
    }
    setIsLoading(false)
  }

  const handleClickLogout = async () => {
    setIsLoading(true)
    await logOut()
    router.refresh()
    setLoggedIn(false)
    setIsLoading(false)
  }

  const kiltIcon = (
    <Image src="/assets/kilt-logo.svg" alt="KILT Logo" width={10} height={4} />
  )

  if (loggedIn) {
    return (
      <Flex align="center" gap={4}>
        <Text fontWeight={600} display="flex" alignItems="center" gap={1}>
          Logged in with {kiltIcon}
        </Text>
        <Button
          size="md"
          variant="outline"
          colorScheme="gray"
          onClick={handleClickLogout}
          isLoading={isLoading}
        >
          Log Out
        </Button>
      </Flex>
    )
  }

  return (
    <>
      <Button
        size="md"
        variant="outline"
        colorScheme="gray"
        onClick={handleClickLoginWithKilt}
        isLoading={isLoading}
        rightIcon={kiltIcon}
        title="Login with KILT"
      >
        Login with
      </Button>
      <MissingSporranModal isOpen={missingSporranModalIsOpen} onClose={() => setMissingSporranModalIsOpen(false)} />
    </>
  )
}

export default Login
