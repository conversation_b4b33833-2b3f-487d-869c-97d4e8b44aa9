NEXT_PUBLIC_KILT_WSS_ADDRESS=wss://peregrine.kilt.io
DAPP_ACCOUNT_MNEMONIC="your account mnemonic here"
DAPP_ACCOUNT_ADDRESS="did:kilt:rePLac3Th1sW1thY0ourDappAccountDID"
NEXT_PUBLIC_DAPP_DID_MNEMONIC="your did mnemonic here"
NEXT_PUBLIC_DAPP_DID_URI="did:kilt:rePLac3Th1sW1thY0ourDappDID"
NEXT_PUBLIC_DAPP_NAME="Decent.ec"
WELL_KNOWN_DID_CONFIGURATION='{"@context": "https://identity.foundation/.well-known/did-configuration/v1","linked_dids": []}'
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN="your mapbox access token here"
EXTERNAL_API_URL="https://api.met3r.com/v0.1"
EXTERNAL_API_USERNAME="your external api username here"
EXTERNAL_API_PASSWORD="your external api password here"
AWS_REGION="your aws region here"
AWS_ACCESS_KEY_ID="your aws access key id here"
AWS_SECRET_ACCESS_KEY="your aws secret access key here"
KEYCLOAK_BASE_URL="https://kc0.decent.ec/auth"
KEYCLOAK_CLIENT_ID="admin-rest-client"
KEYCLOAK_CLIENT_SECRET="rePLac3Th1sW1thY0ourKeycloakClientSecret"
NEXT_PUBLIC_S3_IMAGES_URL="https://s3.amazonaws.com/decent.ec/images"
