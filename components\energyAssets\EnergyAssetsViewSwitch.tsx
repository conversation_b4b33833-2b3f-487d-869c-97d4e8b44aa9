import { Button, ButtonGroup, Flex } from "@chakra-ui/react"
import { MdListAlt, MdOutlineMap } from "react-icons/md"
import React from "react"

type Props = {
  view: "list" | "map"
  onSetView: (view: "list" | "map") => void
}

const EnergyAssetsViewSwitch = ({ view, onSetView }: Props) => (
  <ButtonGroup isAttached>
    <Button
      variant="square-outline"
      leftIcon={<MdListAlt />}
      size="sm"
      {...(view === "list" ? { color: "green.600" } : {})}
      onClick={() => onSetView("list")}
    >
      List View
    </Button>
    <Button
      variant="square-outline"
      leftIcon={<MdOutlineMap />}
      size="sm"
      {...(view === "map" ? { color: "green.600" } : {})}
      onClick={() => onSetView("map")}
    >
      Map View
    </Button>
  </ButtonGroup>
)

export default EnergyAssetsViewSwitch
