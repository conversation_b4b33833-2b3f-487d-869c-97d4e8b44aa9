import React, { useState, useCallback, useEffect } from "react"
import { Feature, Point } from "geojson"
import dynamic from "next/dynamic"
import { Control, FieldValues, Path, useController } from "react-hook-form"
import { FormControl, FormLabel } from "@chakra-ui/react"
const SearchMap = dynamic(() => import("../map/SearchMap"), { ssr: false })

type Props<T extends FieldValues> = {
  label: string
  name: Path<T>
  control: Control<T>
}

const LocationInput = function LocationInput<T extends FieldValues>({
  label,
  name,
  control,
}: Props<T>) {
  const {
    field: { value, onChange },
  } = useController({ name, control })
  const [feature, setFeature] = useState<Feature<Point> | undefined>(value)
  const [showAddressInput, setShowAddressInput] = useState(false)
  const [locationIsSelected, setLocationIsSelected] = useState(false)

  useEffect(() => {
    if (!!feature) return

    navigator.geolocation.getCurrentPosition((position) => {
      setFeature({
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: [position.coords.longitude, position.coords.latitude],
        },
        properties: {},
      })
    })
  }, [feature])

  const handleRetrieve = useCallback(
    (res: { features: Feature<Point>[] }) => {
      const feature = res.features[0]
      setFeature(feature)
      setShowAddressInput(false)
      setLocationIsSelected(true)
      onChange(feature)
    },
    [onChange],
  )

  const handleClickChooseLocation = () => {
    setShowAddressInput(true)
  }

  return (
    <FormControl>
      <FormLabel>{label}</FormLabel>
      <SearchMap
        feature={feature}
        locationIsSelected={locationIsSelected}
        showAddressInput={showAddressInput}
        onRetrieve={handleRetrieve}
        onClickChooseLocation={handleClickChooseLocation}
      />
    </FormControl>
  )
}

export default LocationInput
