import { MdAdd } from "react-icons/md"
import {
  Button,
  IconButton,
  Modal,
  ModalBody,
  ModalCloseButton,
  ModalContent,
  <PERSON>dal<PERSON>ooter,
  ModalHeader,
  ModalOverlay,
  Stack,
} from "@chakra-ui/react"
import React, { useState } from "react"
import Input from "@/components/form/Input"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import useToast from "@/hooks/useToast"
import { postPoolUser } from "@/services/api"
import { User } from "@/app/types/community"
import { extractPoolUser } from "@/services/cognito/cognito"

const schema = z.object({
  username: z.string(),
  email: z.string().email(),
})

type FormValues = {
  username: string
  email: string
}

type Props = {
  onUserAdded: (user: User) => void
}

const AddUser = ({ onUserAdded }: Props) => {
  const toast = useToast()
  const [showModal, setShowModal] = useState<boolean>(false)
  const {
    handleSubmit,
    formState: { isValid, isSubmitting },
    register,
    reset,
  } = useForm<FormValues>({
    resolver: zodResolver(schema),
  })

  const onSubmit = async ({ username, email }: FormValues) => {
    try {
      const user = await postPoolUser(username, email)
      console.log("user", user)
      toast({
        description: `You have successfully added the user ${username}.`,
      })
      onUserAdded(extractPoolUser(user))
      setShowModal(false)
      reset()
    } catch (error: any) {
      toast({
        description: `Error creating user: ${error?.cause?.error?.name}`,
        status: "error",
      })
    }
  }

  const handleClickAdd = () => {
    setShowModal(true)
  }

  const handleCloseModal = () => {
    setShowModal(false)
  }

  return (
    <>
      <IconButton
        icon={<MdAdd />}
        aria-label="Add user"
        variant="outline"
        onClick={handleClickAdd}
      />
      <Modal isOpen={showModal} onClose={handleCloseModal} isCentered>
        <form onSubmit={handleSubmit(onSubmit)}>
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>Add User</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <Stack spacing={4}>
                <Input label="Username" {...register("username")} />
                <Input label="Email" {...register("email")} />
              </Stack>
            </ModalBody>
            <ModalFooter gap={2}>
              <Button onClick={handleCloseModal} variant="ghost">
                Cancel
              </Button>
              <Button
                type="submit"
                isLoading={isSubmitting}
                isDisabled={!isValid}
              >
                Add
              </Button>
            </ModalFooter>
          </ModalContent>
        </form>
      </Modal>
    </>
  )
}

export default AddUser
